"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-from-html";
exports.ids = ["vendor-chunks/hast-util-from-html"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-from-html/lib/errors.js":
/*!********************************************************!*\
  !*** ./node_modules/hast-util-from-html/lib/errors.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   errors: () => (/* binding */ errors)\n/* harmony export */ });\n/**\n * @typedef ErrorInfo\n *   Info on a `parse5` error.\n * @property {string} reason\n *   Reason of error.\n * @property {string} description\n *   More info on error.\n * @property {false} [url]\n *   Turn off if this is not documented in the html5 spec (optional).\n */\n\nconst errors = {\n  /** @type {ErrorInfo} */\n  abandonedHeadElementChild: {\n    reason: 'Unexpected metadata element after head',\n    description:\n      'Unexpected element after head. Expected the element before `</head>`',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  abruptClosingOfEmptyComment: {\n    reason: 'Unexpected abruptly closed empty comment',\n    description: 'Unexpected `>` or `->`. Expected `-->` to close comments'\n  },\n  /** @type {ErrorInfo} */\n  abruptDoctypePublicIdentifier: {\n    reason: 'Unexpected abruptly closed public identifier',\n    description:\n      'Unexpected `>`. Expected a closing `\"` or `\\'` after the public identifier'\n  },\n  /** @type {ErrorInfo} */\n  abruptDoctypeSystemIdentifier: {\n    reason: 'Unexpected abruptly closed system identifier',\n    description:\n      'Unexpected `>`. Expected a closing `\"` or `\\'` after the identifier identifier'\n  },\n  /** @type {ErrorInfo} */\n  absenceOfDigitsInNumericCharacterReference: {\n    reason: 'Unexpected non-digit at start of numeric character reference',\n    description:\n      'Unexpected `%c`. Expected `[0-9]` for decimal references or `[0-9a-fA-F]` for hexadecimal references'\n  },\n  /** @type {ErrorInfo} */\n  cdataInHtmlContent: {\n    reason: 'Unexpected CDATA section in HTML',\n    description:\n      'Unexpected `<![CDATA[` in HTML. Remove it, use a comment, or encode special characters instead'\n  },\n  /** @type {ErrorInfo} */\n  characterReferenceOutsideUnicodeRange: {\n    reason: 'Unexpected too big numeric character reference',\n    description:\n      'Unexpectedly high character reference. Expected character references to be at most hexadecimal 10ffff (or decimal 1114111)'\n  },\n  /** @type {ErrorInfo} */\n  closingOfElementWithOpenChildElements: {\n    reason: 'Unexpected closing tag with open child elements',\n    description:\n      'Unexpectedly closing tag. Expected other tags to be closed first',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  controlCharacterInInputStream: {\n    reason: 'Unexpected control character',\n    description:\n      'Unexpected control character `%x`. Expected a non-control code point, 0x00, or ASCII whitespace'\n  },\n  /** @type {ErrorInfo} */\n  controlCharacterReference: {\n    reason: 'Unexpected control character reference',\n    description:\n      'Unexpectedly control character in reference. Expected a non-control code point, 0x00, or ASCII whitespace'\n  },\n  /** @type {ErrorInfo} */\n  disallowedContentInNoscriptInHead: {\n    reason: 'Disallowed content inside `<noscript>` in `<head>`',\n    description:\n      'Unexpected text character `%c`. Only use text in `<noscript>`s in `<body>`',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  duplicateAttribute: {\n    reason: 'Unexpected duplicate attribute',\n    description:\n      'Unexpectedly double attribute. Expected attributes to occur only once'\n  },\n  /** @type {ErrorInfo} */\n  endTagWithAttributes: {\n    reason: 'Unexpected attribute on closing tag',\n    description: 'Unexpected attribute. Expected `>` instead'\n  },\n  /** @type {ErrorInfo} */\n  endTagWithTrailingSolidus: {\n    reason: 'Unexpected slash at end of closing tag',\n    description: 'Unexpected `%c-1`. Expected `>` instead'\n  },\n  /** @type {ErrorInfo} */\n  endTagWithoutMatchingOpenElement: {\n    reason: 'Unexpected unopened end tag',\n    description: 'Unexpected end tag. Expected no end tag or another end tag',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  eofBeforeTagName: {\n    reason: 'Unexpected end of file',\n    description: 'Unexpected end of file. Expected tag name instead'\n  },\n  /** @type {ErrorInfo} */\n  eofInCdata: {\n    reason: 'Unexpected end of file in CDATA',\n    description: 'Unexpected end of file. Expected `]]>` to close the CDATA'\n  },\n  /** @type {ErrorInfo} */\n  eofInComment: {\n    reason: 'Unexpected end of file in comment',\n    description: 'Unexpected end of file. Expected `-->` to close the comment'\n  },\n  /** @type {ErrorInfo} */\n  eofInDoctype: {\n    reason: 'Unexpected end of file in doctype',\n    description:\n      'Unexpected end of file. Expected a valid doctype (such as `<!doctype html>`)'\n  },\n  /** @type {ErrorInfo} */\n  eofInElementThatCanContainOnlyText: {\n    reason: 'Unexpected end of file in element that can only contain text',\n    description: 'Unexpected end of file. Expected text or a closing tag',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  eofInScriptHtmlCommentLikeText: {\n    reason: 'Unexpected end of file in comment inside script',\n    description: 'Unexpected end of file. Expected `-->` to close the comment'\n  },\n  /** @type {ErrorInfo} */\n  eofInTag: {\n    reason: 'Unexpected end of file in tag',\n    description: 'Unexpected end of file. Expected `>` to close the tag'\n  },\n  /** @type {ErrorInfo} */\n  incorrectlyClosedComment: {\n    reason: 'Incorrectly closed comment',\n    description: 'Unexpected `%c-1`. Expected `-->` to close the comment'\n  },\n  /** @type {ErrorInfo} */\n  incorrectlyOpenedComment: {\n    reason: 'Incorrectly opened comment',\n    description: 'Unexpected `%c`. Expected `<!--` to open the comment'\n  },\n  /** @type {ErrorInfo} */\n  invalidCharacterSequenceAfterDoctypeName: {\n    reason: 'Invalid sequence after doctype name',\n    description: 'Unexpected sequence at `%c`. Expected `public` or `system`'\n  },\n  /** @type {ErrorInfo} */\n  invalidFirstCharacterOfTagName: {\n    reason: 'Invalid first character in tag name',\n    description: 'Unexpected `%c`. Expected an ASCII letter instead'\n  },\n  /** @type {ErrorInfo} */\n  misplacedDoctype: {\n    reason: 'Misplaced doctype',\n    description: 'Unexpected doctype. Expected doctype before head',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  misplacedStartTagForHeadElement: {\n    reason: 'Misplaced `<head>` start tag',\n    description:\n      'Unexpected start tag `<head>`. Expected `<head>` directly after doctype',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  missingAttributeValue: {\n    reason: 'Missing attribute value',\n    description:\n      'Unexpected `%c-1`. Expected an attribute value or no `%c-1` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingDoctype: {\n    reason: 'Missing doctype before other content',\n    description: 'Expected a `<!doctype html>` before anything else',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  missingDoctypeName: {\n    reason: 'Missing doctype name',\n    description: 'Unexpected doctype end at `%c`. Expected `html` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingDoctypePublicIdentifier: {\n    reason: 'Missing public identifier in doctype',\n    description: 'Unexpected `%c`. Expected identifier for `public` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingDoctypeSystemIdentifier: {\n    reason: 'Missing system identifier in doctype',\n    description:\n      'Unexpected `%c`. Expected identifier for `system` instead (suggested: `\"about:legacy-compat\"`)'\n  },\n  /** @type {ErrorInfo} */\n  missingEndTagName: {\n    reason: 'Missing name in end tag',\n    description: 'Unexpected `%c`. Expected an ASCII letter instead'\n  },\n  /** @type {ErrorInfo} */\n  missingQuoteBeforeDoctypePublicIdentifier: {\n    reason: 'Missing quote before public identifier in doctype',\n    description: 'Unexpected `%c`. Expected `\"` or `\\'` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingQuoteBeforeDoctypeSystemIdentifier: {\n    reason: 'Missing quote before system identifier in doctype',\n    description: 'Unexpected `%c`. Expected `\"` or `\\'` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingSemicolonAfterCharacterReference: {\n    reason: 'Missing semicolon after character reference',\n    description: 'Unexpected `%c`. Expected `;` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingWhitespaceAfterDoctypePublicKeyword: {\n    reason: 'Missing whitespace after public identifier in doctype',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  /** @type {ErrorInfo} */\n  missingWhitespaceAfterDoctypeSystemKeyword: {\n    reason: 'Missing whitespace after system identifier in doctype',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  /** @type {ErrorInfo} */\n  missingWhitespaceBeforeDoctypeName: {\n    reason: 'Missing whitespace before doctype name',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  /** @type {ErrorInfo} */\n  missingWhitespaceBetweenAttributes: {\n    reason: 'Missing whitespace between attributes',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  /** @type {ErrorInfo} */\n  missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers: {\n    reason:\n      'Missing whitespace between public and system identifiers in doctype',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  /** @type {ErrorInfo} */\n  nestedComment: {\n    reason: 'Unexpected nested comment',\n    description: 'Unexpected `<!--`. Expected `-->`'\n  },\n  /** @type {ErrorInfo} */\n  nestedNoscriptInHead: {\n    reason: 'Unexpected nested `<noscript>` in `<head>`',\n    description:\n      'Unexpected `<noscript>`. Expected a closing tag or a meta element',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  nonConformingDoctype: {\n    reason: 'Unexpected non-conforming doctype declaration',\n    description:\n      'Expected `<!doctype html>` or `<!doctype html system \"about:legacy-compat\">`',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  nonVoidHtmlElementStartTagWithTrailingSolidus: {\n    reason: 'Unexpected trailing slash on start tag of non-void element',\n    description: 'Unexpected `/`. Expected `>` instead'\n  },\n  /** @type {ErrorInfo} */\n  noncharacterCharacterReference: {\n    reason:\n      'Unexpected noncharacter code point referenced by character reference',\n    description: 'Unexpected code point. Do not use noncharacters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  noncharacterInInputStream: {\n    reason: 'Unexpected noncharacter character',\n    description: 'Unexpected code point `%x`. Do not use noncharacters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  nullCharacterReference: {\n    reason: 'Unexpected NULL character referenced by character reference',\n    description: 'Unexpected code point. Do not use NULL characters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  openElementsLeftAfterEof: {\n    reason: 'Unexpected end of file',\n    description: 'Unexpected end of file. Expected closing tag instead',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  surrogateCharacterReference: {\n    reason: 'Unexpected surrogate character referenced by character reference',\n    description:\n      'Unexpected code point. Do not use lone surrogate characters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  surrogateInInputStream: {\n    reason: 'Unexpected surrogate character',\n    description:\n      'Unexpected code point `%x`. Do not use lone surrogate characters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedCharacterAfterDoctypeSystemIdentifier: {\n    reason: 'Invalid character after system identifier in doctype',\n    description: 'Unexpected character at `%c`. Expected `>`'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedCharacterInAttributeName: {\n    reason: 'Unexpected character in attribute name',\n    description:\n      'Unexpected `%c`. Expected whitespace, `/`, `>`, `=`, or probably an ASCII letter'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedCharacterInUnquotedAttributeValue: {\n    reason: 'Unexpected character in unquoted attribute value',\n    description: 'Unexpected `%c`. Quote the attribute value to include it'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedEqualsSignBeforeAttributeName: {\n    reason: 'Unexpected equals sign before attribute name',\n    description: 'Unexpected `%c`. Add an attribute name before it'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedNullCharacter: {\n    reason: 'Unexpected NULL character',\n    description:\n      'Unexpected code point `%x`. Do not use NULL characters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedQuestionMarkInsteadOfTagName: {\n    reason: 'Unexpected question mark instead of tag name',\n    description: 'Unexpected `%c`. Expected an ASCII letter instead'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedSolidusInTag: {\n    reason: 'Unexpected slash in tag',\n    description:\n      'Unexpected `%c-1`. Expected it followed by `>` or in a quoted attribute value'\n  },\n  /** @type {ErrorInfo} */\n  unknownNamedCharacterReference: {\n    reason: 'Unexpected unknown named character reference',\n    description:\n      'Unexpected character reference. Expected known named character references'\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-from-html/lib/errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-from-html/lib/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/hast-util-from-html/lib/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromHtml: () => (/* binding */ fromHtml)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var hast_util_from_parse5__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-from-parse5 */ \"(ssr)/./node_modules/hast-util-from-parse5/lib/index.js\");\n/* harmony import */ var parse5__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! parse5 */ \"(ssr)/./node_modules/parse5/dist/index.js\");\n/* harmony import */ var vfile__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! vfile */ \"(ssr)/./node_modules/vfile/lib/index.js\");\n/* harmony import */ var vfile_message__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! vfile-message */ \"(ssr)/./node_modules/vfile-message/lib/index.js\");\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./errors.js */ \"(ssr)/./node_modules/hast-util-from-html/lib/errors.js\");\n/**\n * @import {Root} from 'hast'\n * @import {ParserError} from 'parse5'\n * @import {Value} from 'vfile'\n * @import {ErrorCode, Options} from './types.js'\n */\n\n\n\n\n\n\n\n\nconst base = 'https://html.spec.whatwg.org/multipage/parsing.html#parse-error-'\n\nconst dashToCamelRe = /-[a-z]/g\nconst formatCRe = /%c(?:([-+])(\\d+))?/g\nconst formatXRe = /%x/g\n\nconst fatalities = {2: true, 1: false, 0: null}\n\n/** @type {Readonly<Options>} */\nconst emptyOptions = {}\n\n/**\n * Turn serialized HTML into a hast tree.\n *\n * @param {VFile | Value} value\n *   Serialized HTML to parse.\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {Root}\n *   Tree.\n */\nfunction fromHtml(value, options) {\n  const settings = options || emptyOptions\n  const onerror = settings.onerror\n  const file = value instanceof vfile__WEBPACK_IMPORTED_MODULE_1__.VFile ? value : new vfile__WEBPACK_IMPORTED_MODULE_1__.VFile(value)\n  const parseFunction = settings.fragment ? parse5__WEBPACK_IMPORTED_MODULE_0__.parseFragment : parse5__WEBPACK_IMPORTED_MODULE_0__.parse\n  const document = String(file)\n  const p5Document = parseFunction(document, {\n    sourceCodeLocationInfo: true,\n    // Note `parse5` types currently do not allow `undefined`.\n    onParseError: settings.onerror ? internalOnerror : null,\n    scriptingEnabled: false\n  })\n\n  // `parse5` returns document which are always mapped to roots.\n  return /** @type {Root} */ (\n    (0,hast_util_from_parse5__WEBPACK_IMPORTED_MODULE_2__.fromParse5)(p5Document, {\n      file,\n      space: settings.space,\n      verbose: settings.verbose\n    })\n  )\n\n  /**\n   * Handle a parse error.\n   *\n   * @param {ParserError} error\n   *   Parse5 error.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function internalOnerror(error) {\n    const code = error.code\n    const name = camelcase(code)\n    const setting = settings[name]\n    const config = setting === null || setting === undefined ? true : setting\n    const level = typeof config === 'number' ? config : config ? 1 : 0\n\n    if (level) {\n      const info = _errors_js__WEBPACK_IMPORTED_MODULE_3__.errors[name]\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(info, 'expected known error from `parse5`')\n\n      const message = new vfile_message__WEBPACK_IMPORTED_MODULE_5__.VFileMessage(format(info.reason), {\n        place: {\n          start: {\n            line: error.startLine,\n            column: error.startCol,\n            offset: error.startOffset\n          },\n          end: {\n            line: error.endLine,\n            column: error.endCol,\n            offset: error.endOffset\n          }\n        },\n        ruleId: code,\n        source: 'hast-util-from-html'\n      })\n\n      if (file.path) {\n        message.file = file.path\n        message.name = file.path + ':' + message.name\n      }\n\n      message.fatal = fatalities[level]\n      message.note = format(info.description)\n      message.url = info.url === false ? undefined : base + code\n\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(onerror, '`internalOnerror` is not passed if `onerror` is not set')\n      onerror(message)\n    }\n\n    /**\n     * Format a human readable string about an error.\n     *\n     * @param {string} value\n     *   Value to format.\n     * @returns {string}\n     *   Formatted.\n     */\n    function format(value) {\n      return value.replace(formatCRe, formatC).replace(formatXRe, formatX)\n\n      /**\n       * Format the character.\n       *\n       * @param {string} _\n       *   Match.\n       * @param {string} $1\n       *   Sign (`-` or `+`, optional).\n       * @param {string} $2\n       *   Offset.\n       * @returns {string}\n       *   Formatted.\n       */\n      function formatC(_, $1, $2) {\n        const offset =\n          ($2 ? Number.parseInt($2, 10) : 0) * ($1 === '-' ? -1 : 1)\n        const char = document.charAt(error.startOffset + offset)\n        return visualizeCharacter(char)\n      }\n\n      /**\n       * Format the character code.\n       *\n       * @returns {string}\n       *   Formatted.\n       */\n      function formatX() {\n        return visualizeCharacterCode(document.charCodeAt(error.startOffset))\n      }\n    }\n  }\n}\n\n/**\n * @param {string} value\n *   Error code in dash case.\n * @returns {ErrorCode}\n *   Error code in camelcase.\n */\nfunction camelcase(value) {\n  // This should match an error code.\n  return /** @type {ErrorCode} */ (value.replace(dashToCamelRe, dashToCamel))\n}\n\n/**\n * @param {string} $0\n *   Match.\n * @returns {string}\n *   Camelcased.\n */\nfunction dashToCamel($0) {\n  return $0.charAt(1).toUpperCase()\n}\n\n/**\n * @param {string} char\n *   Character.\n * @returns {string}\n *   Formatted.\n */\nfunction visualizeCharacter(char) {\n  return char === '`' ? '` ` `' : char\n}\n\n/**\n * @param {number} charCode\n *   Character code.\n * @returns {string}\n *   Formatted.\n */\nfunction visualizeCharacterCode(charCode) {\n  return '0x' + charCode.toString(16).toUpperCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-from-html/lib/index.js\n");

/***/ })

};
;