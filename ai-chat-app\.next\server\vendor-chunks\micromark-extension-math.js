"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-math";
exports.ids = ["vendor-chunks/micromark-extension-math"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-math/dev/lib/math-flow.js":
/*!********************************************************************!*\
  !*** ./node_modules/micromark-extension-math/dev/lib/math-flow.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mathFlow: () => (/* binding */ mathFlow)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {Construct, State, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\n\n\n\n\n\n/** @type {Construct} */\nconst mathFlow = {\n  tokenize: tokenizeMathFenced,\n  concrete: true,\n  name: 'mathFlow'\n}\n\n/** @type {Construct} */\nconst nonLazyContinuation = {\n  tokenize: tokenizeNonLazyContinuation,\n  partial: true\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeMathFenced(effects, ok, nok) {\n  const self = this\n  const tail = self.events[self.events.length - 1]\n  const initialSize =\n    tail && tail[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix\n      ? tail[2].sliceSerialize(tail[1], true).length\n      : 0\n  let sizeOpen = 0\n\n  return start\n\n  /**\n   * Start of math.\n   *\n   * ```markdown\n   * > | $$\n   *     ^\n   *   | \\frac{1}{2}\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dollarSign, 'expected `$`')\n    effects.enter('mathFlow')\n    effects.enter('mathFlowFence')\n    effects.enter('mathFlowFenceSequence')\n    return sequenceOpen(code)\n  }\n\n  /**\n   * In opening fence sequence.\n   *\n   * ```markdown\n   * > | $$\n   *      ^\n   *   | \\frac{1}{2}\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dollarSign) {\n      effects.consume(code)\n      sizeOpen++\n      return sequenceOpen\n    }\n\n    if (sizeOpen < 2) {\n      return nok(code)\n    }\n\n    effects.exit('mathFlowFenceSequence')\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, metaBefore, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code)\n  }\n\n  /**\n   * In opening fence, before meta.\n   *\n   * ```markdown\n   * > | $$asciimath\n   *       ^\n   *   | x < y\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n\n  function metaBefore(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      return metaAfter(code)\n    }\n\n    effects.enter('mathFlowFenceMeta')\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.chunkString, {contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeString})\n    return meta(code)\n  }\n\n  /**\n   * In meta.\n   *\n   * ```markdown\n   * > | $$asciimath\n   *        ^\n   *   | x < y\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function meta(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.chunkString)\n      effects.exit('mathFlowFenceMeta')\n      return metaAfter(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dollarSign) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return meta\n  }\n\n  /**\n   * After meta.\n   *\n   * ```markdown\n   * > | $$\n   *       ^\n   *   | \\frac{1}{2}\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function metaAfter(code) {\n    // Guaranteed to be eol/eof.\n    effects.exit('mathFlowFence')\n\n    if (self.interrupt) {\n      return ok(code)\n    }\n\n    return effects.attempt(\n      nonLazyContinuation,\n      beforeNonLazyContinuation,\n      after\n    )(code)\n  }\n\n  /**\n   * After eol/eof in math, at a non-lazy closing fence or content.\n   *\n   * ```markdown\n   *   | $$\n   * > | \\frac{1}{2}\n   *     ^\n   * > | $$\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeNonLazyContinuation(code) {\n    return effects.attempt(\n      {tokenize: tokenizeClosingFence, partial: true},\n      after,\n      contentStart\n    )(code)\n  }\n\n  /**\n   * Before math content, definitely not before a closing fence.\n   *\n   * ```markdown\n   *   | $$\n   * > | \\frac{1}{2}\n   *     ^\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function contentStart(code) {\n    return (\n      initialSize\n        ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(\n            effects,\n            beforeContentChunk,\n            micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix,\n            initialSize + 1\n          )\n        : beforeContentChunk\n    )(code)\n  }\n\n  /**\n   * Before math content, after optional prefix.\n   *\n   * ```markdown\n   *   | $$\n   * > | \\frac{1}{2}\n   *     ^\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeContentChunk(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof) {\n      return after(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      return effects.attempt(\n        nonLazyContinuation,\n        beforeNonLazyContinuation,\n        after\n      )(code)\n    }\n\n    effects.enter('mathFlowValue')\n    return contentChunk(code)\n  }\n\n  /**\n   * In math content.\n   *\n   * ```markdown\n   *   | $$\n   * > | \\frac{1}{2}\n   *      ^\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function contentChunk(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      effects.exit('mathFlowValue')\n      return beforeContentChunk(code)\n    }\n\n    effects.consume(code)\n    return contentChunk\n  }\n\n  /**\n   * After math (ha!).\n   *\n   * ```markdown\n   *   | $$\n   *   | \\frac{1}{2}\n   * > | $$\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    effects.exit('mathFlow')\n    return ok(code)\n  }\n\n  /** @type {Tokenizer} */\n  function tokenizeClosingFence(effects, ok, nok) {\n    let size = 0\n\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.parser.constructs.disable.null, 'expected `disable.null`')\n    /**\n     * Before closing fence, at optional whitespace.\n     *\n     * ```markdown\n     *   | $$\n     *   | \\frac{1}{2}\n     * > | $$\n     *     ^\n     * ```\n     */\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(\n      effects,\n      beforeSequenceClose,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix,\n      self.parser.constructs.disable.null.includes('codeIndented')\n        ? undefined\n        : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize\n    )\n\n    /**\n     * In closing fence, after optional whitespace, at sequence.\n     *\n     * ```markdown\n     *   | $$\n     *   | \\frac{1}{2}\n     * > | $$\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function beforeSequenceClose(code) {\n      effects.enter('mathFlowFence')\n      effects.enter('mathFlowFenceSequence')\n      return sequenceClose(code)\n    }\n\n    /**\n     * In closing fence sequence.\n     *\n     * ```markdown\n     *   | $$\n     *   | \\frac{1}{2}\n     * > | $$\n     *      ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceClose(code) {\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dollarSign) {\n        size++\n        effects.consume(code)\n        return sequenceClose\n      }\n\n      if (size < sizeOpen) {\n        return nok(code)\n      }\n\n      effects.exit('mathFlowFenceSequence')\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, afterSequenceClose, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code)\n    }\n\n    /**\n     * After closing fence sequence, after optional whitespace.\n     *\n     * ```markdown\n     *   | $$\n     *   | \\frac{1}{2}\n     * > | $$\n     *       ^\n     * ```\n     *\n     * @type {State}\n     */\n    function afterSequenceClose(code) {\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n        effects.exit('mathFlowFence')\n        return ok(code)\n      }\n\n      return nok(code)\n    }\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeNonLazyContinuation(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    if (code === null) {\n      return ok(code)\n    }\n\n    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code), 'expected eol')\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)\n    return lineStart\n  }\n\n  /** @type {State} */\n  function lineStart(code) {\n    return self.parser.lazy[self.now().line] ? nok(code) : ok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-math/dev/lib/math-flow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-math/dev/lib/math-text.js":
/*!********************************************************************!*\
  !*** ./node_modules/micromark-extension-math/dev/lib/math-text.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mathText: () => (/* binding */ mathText)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/**\n * @import {Options} from 'micromark-extension-math'\n * @import {Construct, Previous, Resolver, State, Token, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\n// To do: next major: clean spaces in HTML compiler.\n// This has to be coordinated together with `mdast-util-math`.\n\n\n\n\n\n/**\n * @param {Options | null | undefined} [options={}]\n *   Configuration (default: `{}`).\n * @returns {Construct}\n *   Construct.\n */\nfunction mathText(options) {\n  const options_ = options || {}\n  let single = options_.singleDollarTextMath\n\n  if (single === null || single === undefined) {\n    single = true\n  }\n\n  return {\n    tokenize: tokenizeMathText,\n    resolve: resolveMathText,\n    previous,\n    name: 'mathText'\n  }\n\n  /**\n   * @this {TokenizeContext}\n   * @type {Tokenizer}\n   */\n  function tokenizeMathText(effects, ok, nok) {\n    const self = this\n    let sizeOpen = 0\n    /** @type {number} */\n    let size\n    /** @type {Token} */\n    let token\n\n    return start\n\n    /**\n     * Start of math (text).\n     *\n     * ```markdown\n     * > | $a$\n     *     ^\n     * > | \\$a$\n     *      ^\n     * ```\n     *\n     * @type {State}\n     */\n    function start(code) {\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign, 'expected `$`')\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(previous.call(self, self.previous), 'expected correct previous')\n      effects.enter('mathText')\n      effects.enter('mathTextSequence')\n      return sequenceOpen(code)\n    }\n\n    /**\n     * In opening sequence.\n     *\n     * ```markdown\n     * > | $a$\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n\n    function sequenceOpen(code) {\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign) {\n        effects.consume(code)\n        sizeOpen++\n        return sequenceOpen\n      }\n\n      // Not enough markers in the sequence.\n      if (sizeOpen < 2 && !single) {\n        return nok(code)\n      }\n\n      effects.exit('mathTextSequence')\n      return between(code)\n    }\n\n    /**\n     * Between something and something else.\n     *\n     * ```markdown\n     * > | $a$\n     *      ^^\n     * ```\n     *\n     * @type {State}\n     */\n    function between(code) {\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n        return nok(code)\n      }\n\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign) {\n        token = effects.enter('mathTextSequence')\n        size = 0\n        return sequenceClose(code)\n      }\n\n      // Tabs don’t work, and virtual spaces don’t make sense.\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.space) {\n        effects.enter('space')\n        effects.consume(code)\n        effects.exit('space')\n        return between\n      }\n\n      if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding)\n        effects.consume(code)\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding)\n        return between\n      }\n\n      // Data.\n      effects.enter('mathTextData')\n      return data(code)\n    }\n\n    /**\n     * In data.\n     *\n     * ```markdown\n     * > | $a$\n     *      ^\n     * ```\n     *\n     * @type {State}\n     */\n    function data(code) {\n      if (\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof ||\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.space ||\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign ||\n        (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)\n      ) {\n        effects.exit('mathTextData')\n        return between(code)\n      }\n\n      effects.consume(code)\n      return data\n    }\n\n    /**\n     * In closing sequence.\n     *\n     * ```markdown\n     * > | `a`\n     *       ^\n     * ```\n     *\n     * @type {State}\n     */\n\n    function sequenceClose(code) {\n      // More.\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign) {\n        effects.consume(code)\n        size++\n        return sequenceClose\n      }\n\n      // Done!\n      if (size === sizeOpen) {\n        effects.exit('mathTextSequence')\n        effects.exit('mathText')\n        return ok(code)\n      }\n\n      // More or less accents: mark as data.\n      token.type = 'mathTextData'\n      return data(code)\n    }\n  }\n}\n\n/** @type {Resolver} */\nfunction resolveMathText(events) {\n  let tailExitIndex = events.length - 4\n  let headEnterIndex = 3\n  /** @type {number} */\n  let index\n  /** @type {number | undefined} */\n  let enter\n\n  // If we start and end with an EOL or a space.\n  if (\n    (events[headEnterIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding ||\n      events[headEnterIndex][1].type === 'space') &&\n    (events[tailExitIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding ||\n      events[tailExitIndex][1].type === 'space')\n  ) {\n    index = headEnterIndex\n\n    // And we have data.\n    while (++index < tailExitIndex) {\n      if (events[index][1].type === 'mathTextData') {\n        // Then we have padding.\n        events[tailExitIndex][1].type = 'mathTextPadding'\n        events[headEnterIndex][1].type = 'mathTextPadding'\n        headEnterIndex += 2\n        tailExitIndex -= 2\n        break\n      }\n    }\n  }\n\n  // Merge adjacent spaces and data.\n  index = headEnterIndex - 1\n  tailExitIndex++\n\n  while (++index <= tailExitIndex) {\n    if (enter === undefined) {\n      if (\n        index !== tailExitIndex &&\n        events[index][1].type !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding\n      ) {\n        enter = index\n      }\n    } else if (\n      index === tailExitIndex ||\n      events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding\n    ) {\n      events[enter][1].type = 'mathTextData'\n\n      if (index !== enter + 2) {\n        events[enter][1].end = events[index - 1][1].end\n        events.splice(enter + 2, index - enter - 2)\n        tailExitIndex -= index - enter - 2\n        index = enter + 2\n      }\n\n      enter = undefined\n    }\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Previous}\n */\nfunction previous(code) {\n  // If there is a previous code, there will always be a tail.\n  return (\n    code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign ||\n    this.events[this.events.length - 1][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.characterEscape\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWV4dGVuc2lvbi1tYXRoL2Rldi9saWIvbWF0aC10ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDQSxZQUFZLFNBQVM7QUFDckIsWUFBWSx5RUFBeUU7QUFDckY7O0FBRUE7QUFDQTs7QUFFbUM7QUFDd0I7QUFDVDs7QUFFbEQ7QUFDQSxXQUFXLDRCQUE0QixXQUFXO0FBQ2xELGdDQUFnQztBQUNoQyxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1A7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsWUFBWTtBQUNaLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QjtBQUNBLGVBQWUsT0FBTztBQUN0Qjs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0EsTUFBTSwyQ0FBTSxVQUFVLHdEQUFLO0FBQzNCLE1BQU0sMkNBQU07QUFDWjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkOztBQUVBO0FBQ0EsbUJBQW1CLHdEQUFLO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQSxtQkFBbUIsd0RBQUs7QUFDeEI7QUFDQTs7QUFFQSxtQkFBbUIsd0RBQUs7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxtQkFBbUIsd0RBQUs7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxVQUFVLDRFQUFrQjtBQUM1QixzQkFBc0Isd0RBQUs7QUFDM0I7QUFDQSxxQkFBcUIsd0RBQUs7QUFDMUI7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQix3REFBSztBQUN0QixpQkFBaUIsd0RBQUs7QUFDdEIsaUJBQWlCLHdEQUFLO0FBQ3RCLFFBQVEsNEVBQWtCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkOztBQUVBO0FBQ0E7QUFDQSxtQkFBbUIsd0RBQUs7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxXQUFXLFVBQVU7QUFDckI7QUFDQTtBQUNBO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCO0FBQ0EsYUFBYSxvQkFBb0I7QUFDakM7O0FBRUE7QUFDQTtBQUNBLHdDQUF3Qyx3REFBSztBQUM3QztBQUNBLHVDQUF1Qyx3REFBSztBQUM1QztBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyx3REFBSztBQUN2QztBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQSxnQ0FBZ0Msd0RBQUs7QUFDckM7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxVQUFVO0FBQ1YsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSx3REFBSztBQUNsQixvREFBb0Qsd0RBQUs7QUFDekQ7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSXFxEZXZcXEFJXFxDaGF0XFxjaGF0MTFcXGFpLWNoYXQtYXBwXFxub2RlX21vZHVsZXNcXG1pY3JvbWFyay1leHRlbnNpb24tbWF0aFxcZGV2XFxsaWJcXG1hdGgtdGV4dC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge09wdGlvbnN9IGZyb20gJ21pY3JvbWFyay1leHRlbnNpb24tbWF0aCdcbiAqIEBpbXBvcnQge0NvbnN0cnVjdCwgUHJldmlvdXMsIFJlc29sdmVyLCBTdGF0ZSwgVG9rZW4sIFRva2VuaXplQ29udGV4dCwgVG9rZW5pemVyfSBmcm9tICdtaWNyb21hcmstdXRpbC10eXBlcydcbiAqL1xuXG4vLyBUbyBkbzogbmV4dCBtYWpvcjogY2xlYW4gc3BhY2VzIGluIEhUTUwgY29tcGlsZXIuXG4vLyBUaGlzIGhhcyB0byBiZSBjb29yZGluYXRlZCB0b2dldGhlciB3aXRoIGBtZGFzdC11dGlsLW1hdGhgLlxuXG5pbXBvcnQge29rIGFzIGFzc2VydH0gZnJvbSAnZGV2bG9wJ1xuaW1wb3J0IHttYXJrZG93bkxpbmVFbmRpbmd9IGZyb20gJ21pY3JvbWFyay11dGlsLWNoYXJhY3RlcidcbmltcG9ydCB7Y29kZXMsIHR5cGVzfSBmcm9tICdtaWNyb21hcmstdXRpbC1zeW1ib2wnXG5cbi8qKlxuICogQHBhcmFtIHtPcHRpb25zIHwgbnVsbCB8IHVuZGVmaW5lZH0gW29wdGlvbnM9e31dXG4gKiAgIENvbmZpZ3VyYXRpb24gKGRlZmF1bHQ6IGB7fWApLlxuICogQHJldHVybnMge0NvbnN0cnVjdH1cbiAqICAgQ29uc3RydWN0LlxuICovXG5leHBvcnQgZnVuY3Rpb24gbWF0aFRleHQob3B0aW9ucykge1xuICBjb25zdCBvcHRpb25zXyA9IG9wdGlvbnMgfHwge31cbiAgbGV0IHNpbmdsZSA9IG9wdGlvbnNfLnNpbmdsZURvbGxhclRleHRNYXRoXG5cbiAgaWYgKHNpbmdsZSA9PT0gbnVsbCB8fCBzaW5nbGUgPT09IHVuZGVmaW5lZCkge1xuICAgIHNpbmdsZSA9IHRydWVcbiAgfVxuXG4gIHJldHVybiB7XG4gICAgdG9rZW5pemU6IHRva2VuaXplTWF0aFRleHQsXG4gICAgcmVzb2x2ZTogcmVzb2x2ZU1hdGhUZXh0LFxuICAgIHByZXZpb3VzLFxuICAgIG5hbWU6ICdtYXRoVGV4dCdcbiAgfVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7VG9rZW5pemVDb250ZXh0fVxuICAgKiBAdHlwZSB7VG9rZW5pemVyfVxuICAgKi9cbiAgZnVuY3Rpb24gdG9rZW5pemVNYXRoVGV4dChlZmZlY3RzLCBvaywgbm9rKSB7XG4gICAgY29uc3Qgc2VsZiA9IHRoaXNcbiAgICBsZXQgc2l6ZU9wZW4gPSAwXG4gICAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gICAgbGV0IHNpemVcbiAgICAvKiogQHR5cGUge1Rva2VufSAqL1xuICAgIGxldCB0b2tlblxuXG4gICAgcmV0dXJuIHN0YXJ0XG5cbiAgICAvKipcbiAgICAgKiBTdGFydCBvZiBtYXRoICh0ZXh0KS5cbiAgICAgKlxuICAgICAqIGBgYG1hcmtkb3duXG4gICAgICogPiB8ICRhJFxuICAgICAqICAgICBeXG4gICAgICogPiB8IFxcJGEkXG4gICAgICogICAgICBeXG4gICAgICogYGBgXG4gICAgICpcbiAgICAgKiBAdHlwZSB7U3RhdGV9XG4gICAgICovXG4gICAgZnVuY3Rpb24gc3RhcnQoY29kZSkge1xuICAgICAgYXNzZXJ0KGNvZGUgPT09IGNvZGVzLmRvbGxhclNpZ24sICdleHBlY3RlZCBgJGAnKVxuICAgICAgYXNzZXJ0KHByZXZpb3VzLmNhbGwoc2VsZiwgc2VsZi5wcmV2aW91cyksICdleHBlY3RlZCBjb3JyZWN0IHByZXZpb3VzJylcbiAgICAgIGVmZmVjdHMuZW50ZXIoJ21hdGhUZXh0JylcbiAgICAgIGVmZmVjdHMuZW50ZXIoJ21hdGhUZXh0U2VxdWVuY2UnKVxuICAgICAgcmV0dXJuIHNlcXVlbmNlT3Blbihjb2RlKVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIEluIG9wZW5pbmcgc2VxdWVuY2UuXG4gICAgICpcbiAgICAgKiBgYGBtYXJrZG93blxuICAgICAqID4gfCAkYSRcbiAgICAgKiAgICAgXlxuICAgICAqIGBgYFxuICAgICAqXG4gICAgICogQHR5cGUge1N0YXRlfVxuICAgICAqL1xuXG4gICAgZnVuY3Rpb24gc2VxdWVuY2VPcGVuKGNvZGUpIHtcbiAgICAgIGlmIChjb2RlID09PSBjb2Rlcy5kb2xsYXJTaWduKSB7XG4gICAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgICBzaXplT3BlbisrXG4gICAgICAgIHJldHVybiBzZXF1ZW5jZU9wZW5cbiAgICAgIH1cblxuICAgICAgLy8gTm90IGVub3VnaCBtYXJrZXJzIGluIHRoZSBzZXF1ZW5jZS5cbiAgICAgIGlmIChzaXplT3BlbiA8IDIgJiYgIXNpbmdsZSkge1xuICAgICAgICByZXR1cm4gbm9rKGNvZGUpXG4gICAgICB9XG5cbiAgICAgIGVmZmVjdHMuZXhpdCgnbWF0aFRleHRTZXF1ZW5jZScpXG4gICAgICByZXR1cm4gYmV0d2Vlbihjb2RlKVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIEJldHdlZW4gc29tZXRoaW5nIGFuZCBzb21ldGhpbmcgZWxzZS5cbiAgICAgKlxuICAgICAqIGBgYG1hcmtkb3duXG4gICAgICogPiB8ICRhJFxuICAgICAqICAgICAgXl5cbiAgICAgKiBgYGBcbiAgICAgKlxuICAgICAqIEB0eXBlIHtTdGF0ZX1cbiAgICAgKi9cbiAgICBmdW5jdGlvbiBiZXR3ZWVuKGNvZGUpIHtcbiAgICAgIGlmIChjb2RlID09PSBjb2Rlcy5lb2YpIHtcbiAgICAgICAgcmV0dXJuIG5vayhjb2RlKVxuICAgICAgfVxuXG4gICAgICBpZiAoY29kZSA9PT0gY29kZXMuZG9sbGFyU2lnbikge1xuICAgICAgICB0b2tlbiA9IGVmZmVjdHMuZW50ZXIoJ21hdGhUZXh0U2VxdWVuY2UnKVxuICAgICAgICBzaXplID0gMFxuICAgICAgICByZXR1cm4gc2VxdWVuY2VDbG9zZShjb2RlKVxuICAgICAgfVxuXG4gICAgICAvLyBUYWJzIGRvbuKAmXQgd29yaywgYW5kIHZpcnR1YWwgc3BhY2VzIGRvbuKAmXQgbWFrZSBzZW5zZS5cbiAgICAgIGlmIChjb2RlID09PSBjb2Rlcy5zcGFjZSkge1xuICAgICAgICBlZmZlY3RzLmVudGVyKCdzcGFjZScpXG4gICAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgICBlZmZlY3RzLmV4aXQoJ3NwYWNlJylcbiAgICAgICAgcmV0dXJuIGJldHdlZW5cbiAgICAgIH1cblxuICAgICAgaWYgKG1hcmtkb3duTGluZUVuZGluZyhjb2RlKSkge1xuICAgICAgICBlZmZlY3RzLmVudGVyKHR5cGVzLmxpbmVFbmRpbmcpXG4gICAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgICBlZmZlY3RzLmV4aXQodHlwZXMubGluZUVuZGluZylcbiAgICAgICAgcmV0dXJuIGJldHdlZW5cbiAgICAgIH1cblxuICAgICAgLy8gRGF0YS5cbiAgICAgIGVmZmVjdHMuZW50ZXIoJ21hdGhUZXh0RGF0YScpXG4gICAgICByZXR1cm4gZGF0YShjb2RlKVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIEluIGRhdGEuXG4gICAgICpcbiAgICAgKiBgYGBtYXJrZG93blxuICAgICAqID4gfCAkYSRcbiAgICAgKiAgICAgIF5cbiAgICAgKiBgYGBcbiAgICAgKlxuICAgICAqIEB0eXBlIHtTdGF0ZX1cbiAgICAgKi9cbiAgICBmdW5jdGlvbiBkYXRhKGNvZGUpIHtcbiAgICAgIGlmIChcbiAgICAgICAgY29kZSA9PT0gY29kZXMuZW9mIHx8XG4gICAgICAgIGNvZGUgPT09IGNvZGVzLnNwYWNlIHx8XG4gICAgICAgIGNvZGUgPT09IGNvZGVzLmRvbGxhclNpZ24gfHxcbiAgICAgICAgbWFya2Rvd25MaW5lRW5kaW5nKGNvZGUpXG4gICAgICApIHtcbiAgICAgICAgZWZmZWN0cy5leGl0KCdtYXRoVGV4dERhdGEnKVxuICAgICAgICByZXR1cm4gYmV0d2Vlbihjb2RlKVxuICAgICAgfVxuXG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIHJldHVybiBkYXRhXG4gICAgfVxuXG4gICAgLyoqXG4gICAgICogSW4gY2xvc2luZyBzZXF1ZW5jZS5cbiAgICAgKlxuICAgICAqIGBgYG1hcmtkb3duXG4gICAgICogPiB8IGBhYFxuICAgICAqICAgICAgIF5cbiAgICAgKiBgYGBcbiAgICAgKlxuICAgICAqIEB0eXBlIHtTdGF0ZX1cbiAgICAgKi9cblxuICAgIGZ1bmN0aW9uIHNlcXVlbmNlQ2xvc2UoY29kZSkge1xuICAgICAgLy8gTW9yZS5cbiAgICAgIGlmIChjb2RlID09PSBjb2Rlcy5kb2xsYXJTaWduKSB7XG4gICAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgICBzaXplKytcbiAgICAgICAgcmV0dXJuIHNlcXVlbmNlQ2xvc2VcbiAgICAgIH1cblxuICAgICAgLy8gRG9uZSFcbiAgICAgIGlmIChzaXplID09PSBzaXplT3Blbikge1xuICAgICAgICBlZmZlY3RzLmV4aXQoJ21hdGhUZXh0U2VxdWVuY2UnKVxuICAgICAgICBlZmZlY3RzLmV4aXQoJ21hdGhUZXh0JylcbiAgICAgICAgcmV0dXJuIG9rKGNvZGUpXG4gICAgICB9XG5cbiAgICAgIC8vIE1vcmUgb3IgbGVzcyBhY2NlbnRzOiBtYXJrIGFzIGRhdGEuXG4gICAgICB0b2tlbi50eXBlID0gJ21hdGhUZXh0RGF0YSdcbiAgICAgIHJldHVybiBkYXRhKGNvZGUpXG4gICAgfVxuICB9XG59XG5cbi8qKiBAdHlwZSB7UmVzb2x2ZXJ9ICovXG5mdW5jdGlvbiByZXNvbHZlTWF0aFRleHQoZXZlbnRzKSB7XG4gIGxldCB0YWlsRXhpdEluZGV4ID0gZXZlbnRzLmxlbmd0aCAtIDRcbiAgbGV0IGhlYWRFbnRlckluZGV4ID0gM1xuICAvKiogQHR5cGUge251bWJlcn0gKi9cbiAgbGV0IGluZGV4XG4gIC8qKiBAdHlwZSB7bnVtYmVyIHwgdW5kZWZpbmVkfSAqL1xuICBsZXQgZW50ZXJcblxuICAvLyBJZiB3ZSBzdGFydCBhbmQgZW5kIHdpdGggYW4gRU9MIG9yIGEgc3BhY2UuXG4gIGlmIChcbiAgICAoZXZlbnRzW2hlYWRFbnRlckluZGV4XVsxXS50eXBlID09PSB0eXBlcy5saW5lRW5kaW5nIHx8XG4gICAgICBldmVudHNbaGVhZEVudGVySW5kZXhdWzFdLnR5cGUgPT09ICdzcGFjZScpICYmXG4gICAgKGV2ZW50c1t0YWlsRXhpdEluZGV4XVsxXS50eXBlID09PSB0eXBlcy5saW5lRW5kaW5nIHx8XG4gICAgICBldmVudHNbdGFpbEV4aXRJbmRleF1bMV0udHlwZSA9PT0gJ3NwYWNlJylcbiAgKSB7XG4gICAgaW5kZXggPSBoZWFkRW50ZXJJbmRleFxuXG4gICAgLy8gQW5kIHdlIGhhdmUgZGF0YS5cbiAgICB3aGlsZSAoKytpbmRleCA8IHRhaWxFeGl0SW5kZXgpIHtcbiAgICAgIGlmIChldmVudHNbaW5kZXhdWzFdLnR5cGUgPT09ICdtYXRoVGV4dERhdGEnKSB7XG4gICAgICAgIC8vIFRoZW4gd2UgaGF2ZSBwYWRkaW5nLlxuICAgICAgICBldmVudHNbdGFpbEV4aXRJbmRleF1bMV0udHlwZSA9ICdtYXRoVGV4dFBhZGRpbmcnXG4gICAgICAgIGV2ZW50c1toZWFkRW50ZXJJbmRleF1bMV0udHlwZSA9ICdtYXRoVGV4dFBhZGRpbmcnXG4gICAgICAgIGhlYWRFbnRlckluZGV4ICs9IDJcbiAgICAgICAgdGFpbEV4aXRJbmRleCAtPSAyXG4gICAgICAgIGJyZWFrXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLy8gTWVyZ2UgYWRqYWNlbnQgc3BhY2VzIGFuZCBkYXRhLlxuICBpbmRleCA9IGhlYWRFbnRlckluZGV4IC0gMVxuICB0YWlsRXhpdEluZGV4KytcblxuICB3aGlsZSAoKytpbmRleCA8PSB0YWlsRXhpdEluZGV4KSB7XG4gICAgaWYgKGVudGVyID09PSB1bmRlZmluZWQpIHtcbiAgICAgIGlmIChcbiAgICAgICAgaW5kZXggIT09IHRhaWxFeGl0SW5kZXggJiZcbiAgICAgICAgZXZlbnRzW2luZGV4XVsxXS50eXBlICE9PSB0eXBlcy5saW5lRW5kaW5nXG4gICAgICApIHtcbiAgICAgICAgZW50ZXIgPSBpbmRleFxuICAgICAgfVxuICAgIH0gZWxzZSBpZiAoXG4gICAgICBpbmRleCA9PT0gdGFpbEV4aXRJbmRleCB8fFxuICAgICAgZXZlbnRzW2luZGV4XVsxXS50eXBlID09PSB0eXBlcy5saW5lRW5kaW5nXG4gICAgKSB7XG4gICAgICBldmVudHNbZW50ZXJdWzFdLnR5cGUgPSAnbWF0aFRleHREYXRhJ1xuXG4gICAgICBpZiAoaW5kZXggIT09IGVudGVyICsgMikge1xuICAgICAgICBldmVudHNbZW50ZXJdWzFdLmVuZCA9IGV2ZW50c1tpbmRleCAtIDFdWzFdLmVuZFxuICAgICAgICBldmVudHMuc3BsaWNlKGVudGVyICsgMiwgaW5kZXggLSBlbnRlciAtIDIpXG4gICAgICAgIHRhaWxFeGl0SW5kZXggLT0gaW5kZXggLSBlbnRlciAtIDJcbiAgICAgICAgaW5kZXggPSBlbnRlciArIDJcbiAgICAgIH1cblxuICAgICAgZW50ZXIgPSB1bmRlZmluZWRcbiAgICB9XG4gIH1cblxuICByZXR1cm4gZXZlbnRzXG59XG5cbi8qKlxuICogQHRoaXMge1Rva2VuaXplQ29udGV4dH1cbiAqIEB0eXBlIHtQcmV2aW91c31cbiAqL1xuZnVuY3Rpb24gcHJldmlvdXMoY29kZSkge1xuICAvLyBJZiB0aGVyZSBpcyBhIHByZXZpb3VzIGNvZGUsIHRoZXJlIHdpbGwgYWx3YXlzIGJlIGEgdGFpbC5cbiAgcmV0dXJuIChcbiAgICBjb2RlICE9PSBjb2Rlcy5kb2xsYXJTaWduIHx8XG4gICAgdGhpcy5ldmVudHNbdGhpcy5ldmVudHMubGVuZ3RoIC0gMV1bMV0udHlwZSA9PT0gdHlwZXMuY2hhcmFjdGVyRXNjYXBlXG4gIClcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-math/dev/lib/math-text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-math/dev/lib/syntax.js":
/*!*****************************************************************!*\
  !*** ./node_modules/micromark-extension-math/dev/lib/syntax.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   math: () => (/* binding */ math)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var _math_flow_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./math-flow.js */ \"(ssr)/./node_modules/micromark-extension-math/dev/lib/math-flow.js\");\n/* harmony import */ var _math_text_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./math-text.js */ \"(ssr)/./node_modules/micromark-extension-math/dev/lib/math-text.js\");\n/**\n * @import {Options} from 'micromark-extension-math'\n * @import {Extension} from 'micromark-util-types'\n */\n\n\n\n\n\n/**\n * Create an extension for `micromark` to enable math syntax.\n *\n * @param {Options | null | undefined} [options={}]\n *   Configuration (default: `{}`).\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions`, to\n *   enable math syntax.\n */\nfunction math(options) {\n  return {\n    flow: {[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dollarSign]: _math_flow_js__WEBPACK_IMPORTED_MODULE_1__.mathFlow},\n    text: {[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dollarSign]: (0,_math_text_js__WEBPACK_IMPORTED_MODULE_2__.mathText)(options)}\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWV4dGVuc2lvbi1tYXRoL2Rldi9saWIvc3ludGF4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUNBLFlBQVksU0FBUztBQUNyQixZQUFZLFdBQVc7QUFDdkI7O0FBRTJDO0FBQ0o7QUFDQTs7QUFFdkM7QUFDQTtBQUNBO0FBQ0EsV0FBVyw0QkFBNEIsV0FBVztBQUNsRCxnQ0FBZ0M7QUFDaEMsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQSxXQUFXLENBQUMsd0RBQUssY0FBYyxtREFBUSxDQUFDO0FBQ3hDLFdBQVcsQ0FBQyx3REFBSyxjQUFjLHVEQUFRO0FBQ3ZDO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRGV2XFxBSVxcQ2hhdFxcY2hhdDExXFxhaS1jaGF0LWFwcFxcbm9kZV9tb2R1bGVzXFxtaWNyb21hcmstZXh0ZW5zaW9uLW1hdGhcXGRldlxcbGliXFxzeW50YXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtPcHRpb25zfSBmcm9tICdtaWNyb21hcmstZXh0ZW5zaW9uLW1hdGgnXG4gKiBAaW1wb3J0IHtFeHRlbnNpb259IGZyb20gJ21pY3JvbWFyay11dGlsLXR5cGVzJ1xuICovXG5cbmltcG9ydCB7Y29kZXN9IGZyb20gJ21pY3JvbWFyay11dGlsLXN5bWJvbCdcbmltcG9ydCB7bWF0aEZsb3d9IGZyb20gJy4vbWF0aC1mbG93LmpzJ1xuaW1wb3J0IHttYXRoVGV4dH0gZnJvbSAnLi9tYXRoLXRleHQuanMnXG5cbi8qKlxuICogQ3JlYXRlIGFuIGV4dGVuc2lvbiBmb3IgYG1pY3JvbWFya2AgdG8gZW5hYmxlIG1hdGggc3ludGF4LlxuICpcbiAqIEBwYXJhbSB7T3B0aW9ucyB8IG51bGwgfCB1bmRlZmluZWR9IFtvcHRpb25zPXt9XVxuICogICBDb25maWd1cmF0aW9uIChkZWZhdWx0OiBge31gKS5cbiAqIEByZXR1cm5zIHtFeHRlbnNpb259XG4gKiAgIEV4dGVuc2lvbiBmb3IgYG1pY3JvbWFya2AgdGhhdCBjYW4gYmUgcGFzc2VkIGluIGBleHRlbnNpb25zYCwgdG9cbiAqICAgZW5hYmxlIG1hdGggc3ludGF4LlxuICovXG5leHBvcnQgZnVuY3Rpb24gbWF0aChvcHRpb25zKSB7XG4gIHJldHVybiB7XG4gICAgZmxvdzoge1tjb2Rlcy5kb2xsYXJTaWduXTogbWF0aEZsb3d9LFxuICAgIHRleHQ6IHtbY29kZXMuZG9sbGFyU2lnbl06IG1hdGhUZXh0KG9wdGlvbnMpfVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-math/dev/lib/syntax.js\n");

/***/ })

};
;