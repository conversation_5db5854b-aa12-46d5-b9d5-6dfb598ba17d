import { Conversation } from '@/types/chat';

// Conditionally import DatabaseService only on server side
let DatabaseService: any = null;
if (typeof window === 'undefined') {
  try {
    DatabaseService = require('@/lib/database').DatabaseService;
  } catch (error) {
    console.error('DatabaseService not available:', error);
    throw new Error('Database service is required for storage operations');
  }
}

// Client-side API helpers
const isClientSide = () => typeof window !== 'undefined';

const apiCall = async (url: string, options?: RequestInit) => {
  try {
    console.log('Making API call:', { url, method: options?.method || 'GET' });
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error');
      console.error('API call failed:', { url, status: response.status, statusText: response.statusText, errorText });
      throw new Error(`API call failed: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const result = await response.json();
    console.log('API call successful:', { url, method: options?.method || 'GET' });
    return result;
  } catch (error) {
    console.error('API call error:', { url, error });
    throw error;
  }
};

// Device ID for identifying this browser/device
const getDeviceId = (): string => {
  if (isClientSide()) {
    let deviceId = localStorage.getItem('nexus-device-id');
    if (!deviceId) {
      deviceId = 'device-' + Math.random().toString(36).substr(2, 9) + '-' + Date.now();
      localStorage.setItem('nexus-device-id', deviceId);
    }
    return deviceId;
  } else {
    // Server-side: generate a temporary device ID or use a default
    return 'server-device-' + Date.now();
  }
};

export class StorageService {
  // Database-only storage - no localStorage fallbacks
  
  static async saveConversations(conversations: Conversation[]): Promise<void> {
    if (isClientSide()) {
      // Client-side: use API endpoint
      const deviceId = getDeviceId();
      await apiCall('/api/storage/conversations', {
        method: 'POST',
        body: JSON.stringify({ deviceId, conversations }),
      });
    } else if (DatabaseService) {
      // Server-side: use DatabaseService directly
      const deviceId = getDeviceId();
      await DatabaseService.saveConversations(deviceId, conversations);
    } else {
      throw new Error('Database service not available');
    }
  }

  static async loadConversations(): Promise<Conversation[]> {
    if (isClientSide()) {
      // Client-side: use API endpoint
      const deviceId = getDeviceId();
      const response = await apiCall(`/api/storage/conversations?deviceId=${encodeURIComponent(deviceId)}`);
      const conversations = response.conversations || [];
      return conversations.sort((a, b) => b.updatedAt - a.updatedAt);
    } else if (DatabaseService) {
      // Server-side: use DatabaseService directly
      const deviceId = getDeviceId();
      const conversations = await DatabaseService.loadConversations(deviceId);
      return conversations.sort((a, b) => b.updatedAt - a.updatedAt);
    } else {
      throw new Error('Database service not available');
    }
  }

  static async saveCurrentConversationId(id: string | null): Promise<void> {
    if (isClientSide()) {
      // Client-side: use API endpoint
      const deviceId = getDeviceId();
      await apiCall('/api/storage/current-conversation', {
        method: 'POST',
        body: JSON.stringify({ deviceId, conversationId: id }),
      });
    } else if (DatabaseService) {
      // Server-side: use DatabaseService directly
      const deviceId = getDeviceId();
      await DatabaseService.setCurrentConversation(deviceId, id);
    } else {
      throw new Error('Database service not available');
    }
  }

  static async loadCurrentConversationId(): Promise<string | null> {
    if (isClientSide()) {
      // Client-side: use API endpoint
      const deviceId = getDeviceId();
      const response = await apiCall(`/api/storage/current-conversation?deviceId=${encodeURIComponent(deviceId)}`);
      return response.conversationId || null;
    } else if (DatabaseService) {
      // Server-side: use DatabaseService directly
      const deviceId = getDeviceId();
      return await DatabaseService.getCurrentConversation(deviceId);
    } else {
      throw new Error('Database service not available');
    }
  }

  static async deleteConversation(id: string): Promise<void> {
    try {
      if (isClientSide()) {
        // Use API endpoint for client-side
        const deviceId = getDeviceId();
        await apiCall(`/api/storage/conversations/${id}?deviceId=${deviceId}`, {
          method: 'DELETE'
        });
        
        // Clear current conversation if it's the one being deleted
        const currentId = await this.loadCurrentConversationId();
        if (currentId === id) {
          await this.saveCurrentConversationId(null);
        }
      } else {
        // Server-side: use DatabaseService directly
        if (DatabaseService) {
          const deviceId = getDeviceId();
          await DatabaseService.deleteConversation(deviceId, id);
        } else {
          // Fallback to local storage manipulation
          const conversations = await this.loadConversations();
          const filtered = conversations.filter(conv => conv.id !== id);
          await this.saveConversations(filtered);
          
          const currentId = await this.loadCurrentConversationId();
          if (currentId === id) {
            await this.saveCurrentConversationId(null);
          }
        }
      }
    } catch (error) {
      console.error('Failed to delete conversation:', error);
      throw error;
    }
  }

  static async updateConversation(conversation: Conversation): Promise<void> {
    try {
      console.log('StorageService.updateConversation called:', {
        conversationId: conversation.id,
        messageCount: conversation.messages.length,
        isClientSide: isClientSide()
      });

      if (isClientSide()) {
        // Check if conversation exists first
        const conversations = await this.loadConversations();
        const existingConversation = conversations.find(conv => conv.id === conversation.id);

        if (existingConversation) {
          // Use PUT endpoint for existing conversations
          const deviceId = getDeviceId();
          console.log('Updating existing conversation via PUT endpoint');
          await apiCall(`/api/storage/conversations/${conversation.id}?deviceId=${deviceId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ conversation: { ...conversation, updatedAt: Date.now() } })
          });
          console.log('Successfully updated existing conversation');
        } else {
          // For new conversations, add to existing conversations and save all
          console.log('Creating new conversation via POST endpoint');
          const updatedConversations = [{ ...conversation, updatedAt: Date.now() }, ...conversations];
          await this.saveConversations(updatedConversations);
          console.log('Successfully created new conversation');
        }
      } else if (DatabaseService) {
        // Server-side: use DatabaseService directly
        const deviceId = getDeviceId();
        console.log('Updating conversation via DatabaseService');
        await DatabaseService.updateSingleConversation(deviceId, { ...conversation, updatedAt: Date.now() });
        console.log('Successfully updated conversation via DatabaseService');
      } else {
        throw new Error('Database service not available');
      }
    } catch (error) {
      console.error('StorageService.updateConversation failed:', error);
      throw error;
    }
  }

  static async renameConversation(id: string, newTitle: string): Promise<void> {
    if (isClientSide()) {
      // Use API endpoint for client-side
      const deviceId = getDeviceId();
      await apiCall(`/api/storage/conversations/${id}?deviceId=${deviceId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ title: newTitle })
      });
    } else if (DatabaseService) {
      // Server-side: use DatabaseService directly
      const deviceId = getDeviceId();
      await DatabaseService.updateConversationTitle(deviceId, id, newTitle);
    } else {
      throw new Error('Database service not available');
    }
  }

  static async deleteMessage(conversationId: string, messageId: string): Promise<void> {
    try {
      if (isClientSide()) {
        // Use API endpoint for client-side
        const deviceId = getDeviceId();
        await apiCall(`/api/storage/conversations/${conversationId}/messages/${messageId}?deviceId=${deviceId}`, {
          method: 'DELETE'
        });
      } else {
        // Server-side: use DatabaseService directly
        if (DatabaseService) {
          const deviceId = getDeviceId();
          await DatabaseService.deleteMessage(deviceId, conversationId, messageId);
        } else {
          throw new Error('Database service not available');
        }
      }
    } catch (error) {
      console.error('Failed to delete message:', error);
      throw error;
    }
  }

  static async clearAllData(): Promise<void> {
    if (isClientSide()) {
      // Use API endpoint to clear data
      const deviceId = getDeviceId();
      await apiCall('/api/storage/clear', {
        method: 'DELETE',
        body: JSON.stringify({ deviceId })
      });
      // Also clear device ID from localStorage
      localStorage.removeItem('nexus-device-id');
    } else if (DatabaseService) {
      // Server-side: use DatabaseService directly
      const deviceId = getDeviceId();
      await DatabaseService.clearUserData(deviceId);
    } else {
      throw new Error('Database service not available');
    }
  }

  static async initialize(): Promise<void> {
    if (DatabaseService) {
      // Initialize database service
      await DatabaseService.initialize();
      console.log('Storage service initialized with database support');
    } else if (isClientSide()) {
      console.log('Storage service initialized for client-side API calls');
    } else {
      throw new Error('Database service not available on server-side');
    }
  }

  // Utility methods for compatibility
  static async getStorageInfo(): Promise<{ version: string; conversationCount: number; hasCurrentConversation: boolean }> {
    try {
      const conversations = await this.loadConversations();
      const currentId = await this.loadCurrentConversationId();
      
      return {
        version: 'database',
        conversationCount: conversations.length,
        hasCurrentConversation: !!currentId
      };
    } catch (error) {
      console.error('Failed to get storage info:', error);
      return {
        version: 'unknown',
        conversationCount: 0,
        hasCurrentConversation: false
      };
    }
  }
}
