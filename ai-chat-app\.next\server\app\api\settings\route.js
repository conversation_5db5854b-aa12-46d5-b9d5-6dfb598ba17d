/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/settings/route";
exports.ids = ["app/api/settings/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsettings%2Froute&page=%2Fapi%2Fsettings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Froute.ts&appDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsettings%2Froute&page=%2Fapi%2Fsettings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Froute.ts&appDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var C_Users_USER_Dev_AI_Chat_chat11_ai_chat_app_src_app_api_settings_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/settings/route.ts */ \"(rsc)/./src/app/api/settings/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_USER_Dev_AI_Chat_chat11_ai_chat_app_src_app_api_settings_route_ts__WEBPACK_IMPORTED_MODULE_16__]);\nC_Users_USER_Dev_AI_Chat_chat11_ai_chat_app_src_app_api_settings_route_ts__WEBPACK_IMPORTED_MODULE_16__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/settings/route\",\n        pathname: \"/api/settings\",\n        filename: \"route\",\n        bundlePath: \"app/api/settings/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\app\\\\api\\\\settings\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_USER_Dev_AI_Chat_chat11_ai_chat_app_src_app_api_settings_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/settings/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsettings%2Froute&page=%2Fapi%2Fsettings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Froute.ts&appDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/settings/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/settings/route.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_database__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_database__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// In a real application, you would store settings in a database\n// For now, we'll use a simple in-memory store or file system\n// This is a simplified implementation for demonstration\nconst SETTINGS_STORAGE_KEY = 'user_settings';\n// Get default settings with environment variable fallbacks\nfunction getDefaultSettings() {\n    return {\n        theme: 'auto',\n        defaultMode: 'simple',\n        autoSave: true,\n        preferredModels: {\n            simple: 'Qwen/Qwen3-235B-A22B-Instruct-2507',\n            enhanced: 'Qwen/Qwen3-235B-A22B-Instruct-2507',\n            orchestrator: 'Qwen/Qwen3-235B-A22B-Instruct-2507',\n            refine: 'Qwen/Qwen3-235B-A22B-Instruct-2507',\n            memorySummarization: 'Qwen/Qwen3-235B-A22B-Instruct-2507'\n        },\n        providerSettings: {\n            baseUrl: process.env.CHUTES_API_URL || 'https://llm.chutes.ai/v1',\n            apiKey: \"cpk_be0507bf8b1c4e8e92323cc5b59b6d6f.a6aa7553cf3857ee8a6d07b5ceb6b6c6.GqbvdwyFEK53nS3xfB6YVke4HdD5RA5G\" || 0,\n            availableModels: []\n        },\n        agentSettings: {\n            maxIterations: 5,\n            temperature: 0.7,\n            enableTools: [\n                'web_search',\n                'calculator',\n                'file_operations'\n            ]\n        },\n        orchestratorSettings: {\n            parallelAgents: 3,\n            taskTimeout: 300,\n            aggregationStrategy: 'consensus',\n            enableCollaboration: true\n        },\n        zepSettings: {\n            enabled: true,\n            apiUrl: process.env.ZEP_API_URL || 'http://localhost:8001',\n            apiKey: \"z_1dWlkIjoiYTY2YzRhYjYtYzAzZS00OWI2LWJlYzQtZDQxN2Q3MzI5MWUyIn0.Y9nPa8oAVDaWZ4t45baO4bSfeo4jhEDP1LbD2zWLAjP77j8819_K8GQPpjpV1hLu85ULtdwUFfYfUKN8NB2Qgw\" || 0 || 0,\n            autoMemoryExtraction: true,\n            memoryRetentionDays: 30,\n            showMemoryInsights: true,\n            minFactRating: 7\n        }\n    };\n}\n// Settings are now stored in the database\n// Helper function to merge settings with defaults, ensuring environment variables take precedence\nfunction mergeWithDefaults(settings) {\n    const defaults = getDefaultSettings();\n    const merged = {\n        ...defaults,\n        ...settings,\n        preferredModels: {\n            ...defaults.preferredModels,\n            ...settings.preferredModels\n        },\n        providerSettings: {\n            ...defaults.providerSettings,\n            ...settings.providerSettings,\n            // Always override API key with environment variable (never from user settings)\n            apiKey: \"cpk_be0507bf8b1c4e8e92323cc5b59b6d6f.a6aa7553cf3857ee8a6d07b5ceb6b6c6.GqbvdwyFEK53nS3xfB6YVke4HdD5RA5G\" || 0\n        },\n        agentSettings: {\n            ...defaults.agentSettings,\n            ...settings.agentSettings\n        },\n        orchestratorSettings: {\n            ...defaults.orchestratorSettings,\n            ...settings.orchestratorSettings\n        },\n        zepSettings: {\n            ...defaults.zepSettings,\n            ...settings.zepSettings,\n            // Always override API key with environment variable (never from user settings)\n            apiKey: \"z_1dWlkIjoiYTY2YzRhYjYtYzAzZS00OWI2LWJlYzQtZDQxN2Q3MzI5MWUyIn0.Y9nPa8oAVDaWZ4t45baO4bSfeo4jhEDP1LbD2zWLAjP77j8819_K8GQPpjpV1hLu85ULtdwUFfYfUKN8NB2Qgw\" || 0 || 0\n        }\n    };\n    return merged;\n}\n// GET - Retrieve user settings\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const userId = searchParams.get('userId');\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'User ID required'\n            }, {\n                status: 400\n            });\n        }\n        const settings = await _lib_database__WEBPACK_IMPORTED_MODULE_1__.DatabaseService.loadUserSettings(userId, 'userSettings');\n        const mergedSettings = mergeWithDefaults(settings || {});\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(mergedSettings);\n    } catch (error) {\n        console.error('Error fetching settings:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch settings'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - Update user settings\nasync function POST(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const userId = searchParams.get('userId');\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'User ID required'\n            }, {\n                status: 400\n            });\n        }\n        const updates = await request.json();\n        // Validate the updates (basic validation)\n        if (typeof updates !== 'object' || updates === null) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid settings format'\n            }, {\n                status: 400\n            });\n        }\n        try {\n            // Try database first\n            if (await _lib_database__WEBPACK_IMPORTED_MODULE_1__.DatabaseService.healthCheck()) {\n                const existingSettings = await _lib_database__WEBPACK_IMPORTED_MODULE_1__.DatabaseService.loadUserSettings(userId, 'userSettings') || {};\n                const defaults = getDefaultSettings();\n                // Merge updates with existing settings, using defaults as base\n                const mergedSettings = {\n                    ...defaults,\n                    ...existingSettings,\n                    ...updates,\n                    preferredModels: {\n                        ...defaults.preferredModels,\n                        ...existingSettings.preferredModels,\n                        ...updates.preferredModels\n                    },\n                    providerSettings: {\n                        ...defaults.providerSettings,\n                        ...existingSettings.providerSettings,\n                        ...updates.providerSettings,\n                        // Always override API key with environment variable\n                        apiKey: \"cpk_be0507bf8b1c4e8e92323cc5b59b6d6f.a6aa7553cf3857ee8a6d07b5ceb6b6c6.GqbvdwyFEK53nS3xfB6YVke4HdD5RA5G\" || 0\n                    },\n                    agentSettings: {\n                        ...defaults.agentSettings,\n                        ...existingSettings.agentSettings,\n                        ...updates.agentSettings\n                    },\n                    orchestratorSettings: {\n                        ...defaults.orchestratorSettings,\n                        ...existingSettings.orchestratorSettings,\n                        ...updates.orchestratorSettings\n                    },\n                    zepSettings: {\n                        ...defaults.zepSettings,\n                        ...existingSettings.zepSettings,\n                        ...updates.zepSettings,\n                        // Always override API key with environment variable\n                        apiKey: \"z_1dWlkIjoiYTY2YzRhYjYtYzAzZS00OWI2LWJlYzQtZDQxN2Q3MzI5MWUyIn0.Y9nPa8oAVDaWZ4t45baO4bSfeo4jhEDP1LbD2zWLAjP77j8819_K8GQPpjpV1hLu85ULtdwUFfYfUKN8NB2Qgw\" || 0 || 0\n                    }\n                };\n                await _lib_database__WEBPACK_IMPORTED_MODULE_1__.DatabaseService.saveUserSettings(userId, 'userSettings', mergedSettings);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true\n                });\n            } else {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Database service unavailable'\n                }, {\n                    status: 503\n                });\n            }\n        } catch (dbError) {\n            console.error('Database error:', dbError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Database operation failed'\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error('Error updating settings:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to update settings'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - Reset settings to defaults\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const userId = searchParams.get('userId');\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'User ID required'\n            }, {\n                status: 400\n            });\n        }\n        // Clear user settings from database (reset to defaults)\n        await _lib_database__WEBPACK_IMPORTED_MODULE_1__.DatabaseService.saveUserSettings(userId, 'userSettings', {});\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true\n        });\n    } catch (error) {\n        console.error('Error resetting settings:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to reset settings'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9zZXR0aW5ncy9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUF3RDtBQUVQO0FBRWpELGdFQUFnRTtBQUNoRSw2REFBNkQ7QUFDN0Qsd0RBQXdEO0FBRXhELE1BQU1FLHVCQUF1QjtBQUU3QiwyREFBMkQ7QUFDM0QsU0FBU0M7SUFDUCxPQUFPO1FBQ0xDLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxVQUFVO1FBQ1ZDLGlCQUFpQjtZQUNmQyxRQUFRO1lBQ1JDLFVBQVU7WUFDVkMsY0FBYztZQUNkQyxRQUFRO1lBQ1JDLHFCQUFxQjtRQUN2QjtRQUNBQyxrQkFBa0I7WUFDaEJDLFNBQVNDLFFBQVFDLEdBQUcsQ0FBQ0MsY0FBYyxJQUFJO1lBQ3ZDQyxRQUFRSCx3R0FBd0MsSUFBSSxDQUFFO1lBQ3RESyxpQkFBaUIsRUFBRTtRQUNyQjtRQUNBQyxlQUFlO1lBQ2JDLGVBQWU7WUFDZkMsYUFBYTtZQUNiQyxhQUFhO2dCQUFDO2dCQUFjO2dCQUFjO2FBQWtCO1FBQzlEO1FBQ0FDLHNCQUFzQjtZQUNwQkMsZ0JBQWdCO1lBQ2hCQyxhQUFhO1lBQ2JDLHFCQUFxQjtZQUNyQkMscUJBQXFCO1FBQ3ZCO1FBQ0FDLGFBQWE7WUFDWEMsU0FBUztZQUNUQyxRQUFRakIsUUFBUUMsR0FBRyxDQUFDaUIsV0FBVyxJQUFJO1lBQ25DZixRQUFRSCx1SkFBbUMsSUFBSUEsQ0FBdUIsSUFBSSxDQUEyQjtZQUNyR3FCLHNCQUFzQjtZQUN0QkMscUJBQXFCO1lBQ3JCQyxvQkFBb0I7WUFDcEJDLGVBQWU7UUFDakI7SUFDRjtBQUNGO0FBRUEsMENBQTBDO0FBRTFDLGtHQUFrRztBQUNsRyxTQUFTQyxrQkFBa0JDLFFBQStCO0lBQ3hELE1BQU1DLFdBQVd2QztJQUVqQixNQUFNd0MsU0FBUztRQUNiLEdBQUdELFFBQVE7UUFDWCxHQUFHRCxRQUFRO1FBQ1hsQyxpQkFBaUI7WUFDZixHQUFHbUMsU0FBU25DLGVBQWU7WUFDM0IsR0FBR2tDLFNBQVNsQyxlQUFlO1FBQzdCO1FBQ0FNLGtCQUFrQjtZQUNoQixHQUFHNkIsU0FBUzdCLGdCQUFnQjtZQUM1QixHQUFHNEIsU0FBUzVCLGdCQUFnQjtZQUM1QiwrRUFBK0U7WUFDL0VLLFFBQVFILHdHQUF3QyxJQUFJLENBQUU7UUFDeEQ7UUFDQU0sZUFBZTtZQUNiLEdBQUdxQixTQUFTckIsYUFBYTtZQUN6QixHQUFHb0IsU0FBU3BCLGFBQWE7UUFDM0I7UUFDQUksc0JBQXNCO1lBQ3BCLEdBQUdpQixTQUFTakIsb0JBQW9CO1lBQ2hDLEdBQUdnQixTQUFTaEIsb0JBQW9CO1FBQ2xDO1FBQ0FLLGFBQWE7WUFDWCxHQUFHWSxTQUFTWixXQUFXO1lBQ3ZCLEdBQUdXLFNBQVNYLFdBQVc7WUFDdkIsK0VBQStFO1lBQy9FWixRQUFRSCx1SkFBbUMsSUFBSUEsQ0FBdUIsSUFBSSxDQUEyQjtRQUN2RztJQUNGO0lBRUEsT0FBTzRCO0FBQ1Q7QUFFQSwrQkFBK0I7QUFDeEIsZUFBZUMsSUFBSUMsT0FBb0I7SUFDNUMsSUFBSTtRQUNGLE1BQU0sRUFBRUMsWUFBWSxFQUFFLEdBQUcsSUFBSUMsSUFBSUYsUUFBUUcsR0FBRztRQUM1QyxNQUFNQyxTQUFTSCxhQUFhSSxHQUFHLENBQUM7UUFFaEMsSUFBSSxDQUFDRCxRQUFRO1lBQ1gsT0FBT2pELHFEQUFZQSxDQUFDbUQsSUFBSSxDQUFDO2dCQUFFQyxPQUFPO1lBQW1CLEdBQUc7Z0JBQUVDLFFBQVE7WUFBSTtRQUN4RTtRQUVBLE1BQU1aLFdBQVcsTUFBTXhDLDBEQUFlQSxDQUFDcUQsZ0JBQWdCLENBQUNMLFFBQVE7UUFDaEUsTUFBTU0saUJBQWlCZixrQkFBa0JDLFlBQVksQ0FBQztRQUN0RCxPQUFPekMscURBQVlBLENBQUNtRCxJQUFJLENBQUNJO0lBQzNCLEVBQUUsT0FBT0gsT0FBTztRQUNkSSxRQUFRSixLQUFLLENBQUMsNEJBQTRCQTtRQUMxQyxPQUFPcEQscURBQVlBLENBQUNtRCxJQUFJLENBQ3RCO1lBQUVDLE9BQU87UUFBMkIsR0FDcEM7WUFBRUMsUUFBUTtRQUFJO0lBRWxCO0FBQ0Y7QUFFQSw4QkFBOEI7QUFDdkIsZUFBZUksS0FBS1osT0FBb0I7SUFDN0MsSUFBSTtRQUNGLE1BQU0sRUFBRUMsWUFBWSxFQUFFLEdBQUcsSUFBSUMsSUFBSUYsUUFBUUcsR0FBRztRQUM1QyxNQUFNQyxTQUFTSCxhQUFhSSxHQUFHLENBQUM7UUFFaEMsSUFBSSxDQUFDRCxRQUFRO1lBQ1gsT0FBT2pELHFEQUFZQSxDQUFDbUQsSUFBSSxDQUFDO2dCQUFFQyxPQUFPO1lBQW1CLEdBQUc7Z0JBQUVDLFFBQVE7WUFBSTtRQUN4RTtRQUVBLE1BQU1LLFVBQWlDLE1BQU1iLFFBQVFNLElBQUk7UUFFekQsMENBQTBDO1FBQzFDLElBQUksT0FBT08sWUFBWSxZQUFZQSxZQUFZLE1BQU07WUFDbkQsT0FBTzFELHFEQUFZQSxDQUFDbUQsSUFBSSxDQUN0QjtnQkFBRUMsT0FBTztZQUEwQixHQUNuQztnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsSUFBSTtZQUNGLHFCQUFxQjtZQUNyQixJQUFJLE1BQU1wRCwwREFBZUEsQ0FBQzBELFdBQVcsSUFBSTtnQkFDdkMsTUFBTUMsbUJBQW1CLE1BQU0zRCwwREFBZUEsQ0FBQ3FELGdCQUFnQixDQUFDTCxRQUFRLG1CQUFtQixDQUFDO2dCQUM1RixNQUFNUCxXQUFXdkM7Z0JBRWpCLCtEQUErRDtnQkFDL0QsTUFBTW9ELGlCQUFpQjtvQkFDckIsR0FBR2IsUUFBUTtvQkFDWCxHQUFHa0IsZ0JBQWdCO29CQUNuQixHQUFHRixPQUFPO29CQUNWbkQsaUJBQWlCO3dCQUNmLEdBQUdtQyxTQUFTbkMsZUFBZTt3QkFDM0IsR0FBR3FELGlCQUFpQnJELGVBQWU7d0JBQ25DLEdBQUdtRCxRQUFRbkQsZUFBZTtvQkFDNUI7b0JBQ0FNLGtCQUFrQjt3QkFDaEIsR0FBRzZCLFNBQVM3QixnQkFBZ0I7d0JBQzVCLEdBQUcrQyxpQkFBaUIvQyxnQkFBZ0I7d0JBQ3BDLEdBQUc2QyxRQUFRN0MsZ0JBQWdCO3dCQUMzQixvREFBb0Q7d0JBQ3BESyxRQUFRSCx3R0FBd0MsSUFBSSxDQUFFO29CQUN4RDtvQkFDQU0sZUFBZTt3QkFDYixHQUFHcUIsU0FBU3JCLGFBQWE7d0JBQ3pCLEdBQUd1QyxpQkFBaUJ2QyxhQUFhO3dCQUNqQyxHQUFHcUMsUUFBUXJDLGFBQWE7b0JBQzFCO29CQUNBSSxzQkFBc0I7d0JBQ3BCLEdBQUdpQixTQUFTakIsb0JBQW9CO3dCQUNoQyxHQUFHbUMsaUJBQWlCbkMsb0JBQW9CO3dCQUN4QyxHQUFHaUMsUUFBUWpDLG9CQUFvQjtvQkFDakM7b0JBQ0FLLGFBQWE7d0JBQ1gsR0FBR1ksU0FBU1osV0FBVzt3QkFDdkIsR0FBRzhCLGlCQUFpQjlCLFdBQVc7d0JBQy9CLEdBQUc0QixRQUFRNUIsV0FBVzt3QkFDdEIsb0RBQW9EO3dCQUNwRFosUUFBUUgsdUpBQW1DLElBQUlBLENBQXVCLElBQUksQ0FBMkI7b0JBQ3ZHO2dCQUNGO2dCQUVBLE1BQU1kLDBEQUFlQSxDQUFDNEQsZ0JBQWdCLENBQUNaLFFBQVEsZ0JBQWdCTTtnQkFFL0QsT0FBT3ZELHFEQUFZQSxDQUFDbUQsSUFBSSxDQUFDO29CQUFFVyxTQUFTO2dCQUFLO1lBQzNDLE9BQU87Z0JBQ0wsT0FBTzlELHFEQUFZQSxDQUFDbUQsSUFBSSxDQUN0QjtvQkFBRUMsT0FBTztnQkFBK0IsR0FDeEM7b0JBQUVDLFFBQVE7Z0JBQUk7WUFFbEI7UUFDRixFQUFFLE9BQU9VLFNBQVM7WUFDaEJQLFFBQVFKLEtBQUssQ0FBQyxtQkFBbUJXO1lBQ2pDLE9BQU8vRCxxREFBWUEsQ0FBQ21ELElBQUksQ0FDdEI7Z0JBQUVDLE9BQU87WUFBNEIsR0FDckM7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtJQUNGLEVBQUUsT0FBT0QsT0FBTztRQUNkSSxRQUFRSixLQUFLLENBQUMsNEJBQTRCQTtRQUMxQyxPQUFPcEQscURBQVlBLENBQUNtRCxJQUFJLENBQ3RCO1lBQUVDLE9BQU87UUFBNEIsR0FDckM7WUFBRUMsUUFBUTtRQUFJO0lBRWxCO0FBQ0Y7QUFFQSxzQ0FBc0M7QUFDL0IsZUFBZVcsT0FBT25CLE9BQW9CO0lBQy9DLElBQUk7UUFDRixNQUFNLEVBQUVDLFlBQVksRUFBRSxHQUFHLElBQUlDLElBQUlGLFFBQVFHLEdBQUc7UUFDNUMsTUFBTUMsU0FBU0gsYUFBYUksR0FBRyxDQUFDO1FBRWhDLElBQUksQ0FBQ0QsUUFBUTtZQUNYLE9BQU9qRCxxREFBWUEsQ0FBQ21ELElBQUksQ0FBQztnQkFBRUMsT0FBTztZQUFtQixHQUFHO2dCQUFFQyxRQUFRO1lBQUk7UUFDeEU7UUFFQSx3REFBd0Q7UUFDeEQsTUFBTXBELDBEQUFlQSxDQUFDNEQsZ0JBQWdCLENBQUNaLFFBQVEsZ0JBQWdCLENBQUM7UUFFaEUsT0FBT2pELHFEQUFZQSxDQUFDbUQsSUFBSSxDQUFDO1lBQUVXLFNBQVM7UUFBSztJQUMzQyxFQUFFLE9BQU9WLE9BQU87UUFDZEksUUFBUUosS0FBSyxDQUFDLDZCQUE2QkE7UUFDM0MsT0FBT3BELHFEQUFZQSxDQUFDbUQsSUFBSSxDQUN0QjtZQUFFQyxPQUFPO1FBQTJCLEdBQ3BDO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVTRVJcXERldlxcQUlcXENoYXRcXGNoYXQxMVxcYWktY2hhdC1hcHBcXHNyY1xcYXBwXFxhcGlcXHNldHRpbmdzXFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgVXNlclNldHRpbmdzIH0gZnJvbSAnQC9jb250ZXh0cy9TZXR0aW5nc0NvbnRleHQnO1xuaW1wb3J0IHsgRGF0YWJhc2VTZXJ2aWNlIH0gZnJvbSAnQC9saWIvZGF0YWJhc2UnO1xuXG4vLyBJbiBhIHJlYWwgYXBwbGljYXRpb24sIHlvdSB3b3VsZCBzdG9yZSBzZXR0aW5ncyBpbiBhIGRhdGFiYXNlXG4vLyBGb3Igbm93LCB3ZSdsbCB1c2UgYSBzaW1wbGUgaW4tbWVtb3J5IHN0b3JlIG9yIGZpbGUgc3lzdGVtXG4vLyBUaGlzIGlzIGEgc2ltcGxpZmllZCBpbXBsZW1lbnRhdGlvbiBmb3IgZGVtb25zdHJhdGlvblxuXG5jb25zdCBTRVRUSU5HU19TVE9SQUdFX0tFWSA9ICd1c2VyX3NldHRpbmdzJztcblxuLy8gR2V0IGRlZmF1bHQgc2V0dGluZ3Mgd2l0aCBlbnZpcm9ubWVudCB2YXJpYWJsZSBmYWxsYmFja3NcbmZ1bmN0aW9uIGdldERlZmF1bHRTZXR0aW5ncygpOiBVc2VyU2V0dGluZ3Mge1xuICByZXR1cm4ge1xuICAgIHRoZW1lOiAnYXV0bycsXG4gICAgZGVmYXVsdE1vZGU6ICdzaW1wbGUnLFxuICAgIGF1dG9TYXZlOiB0cnVlLFxuICAgIHByZWZlcnJlZE1vZGVsczoge1xuICAgICAgc2ltcGxlOiAnUXdlbi9Rd2VuMy0yMzVCLUEyMkItSW5zdHJ1Y3QtMjUwNycsXG4gICAgICBlbmhhbmNlZDogJ1F3ZW4vUXdlbjMtMjM1Qi1BMjJCLUluc3RydWN0LTI1MDcnLFxuICAgICAgb3JjaGVzdHJhdG9yOiAnUXdlbi9Rd2VuMy0yMzVCLUEyMkItSW5zdHJ1Y3QtMjUwNycsXG4gICAgICByZWZpbmU6ICdRd2VuL1F3ZW4zLTIzNUItQTIyQi1JbnN0cnVjdC0yNTA3JyxcbiAgICAgIG1lbW9yeVN1bW1hcml6YXRpb246ICdRd2VuL1F3ZW4zLTIzNUItQTIyQi1JbnN0cnVjdC0yNTA3J1xuICAgIH0sXG4gICAgcHJvdmlkZXJTZXR0aW5nczoge1xuICAgICAgYmFzZVVybDogcHJvY2Vzcy5lbnYuQ0hVVEVTX0FQSV9VUkwgfHwgJ2h0dHBzOi8vbGxtLmNodXRlcy5haS92MScsXG4gICAgICBhcGlLZXk6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0NIVVRFU19BUElfVE9LRU4gfHwgJycsIC8vIEFsd2F5cyBmcm9tIGVudlxuICAgICAgYXZhaWxhYmxlTW9kZWxzOiBbXVxuICAgIH0sXG4gICAgYWdlbnRTZXR0aW5nczoge1xuICAgICAgbWF4SXRlcmF0aW9uczogNSxcbiAgICAgIHRlbXBlcmF0dXJlOiAwLjcsXG4gICAgICBlbmFibGVUb29sczogWyd3ZWJfc2VhcmNoJywgJ2NhbGN1bGF0b3InLCAnZmlsZV9vcGVyYXRpb25zJ11cbiAgICB9LFxuICAgIG9yY2hlc3RyYXRvclNldHRpbmdzOiB7XG4gICAgICBwYXJhbGxlbEFnZW50czogMyxcbiAgICAgIHRhc2tUaW1lb3V0OiAzMDAsXG4gICAgICBhZ2dyZWdhdGlvblN0cmF0ZWd5OiAnY29uc2Vuc3VzJyxcbiAgICAgIGVuYWJsZUNvbGxhYm9yYXRpb246IHRydWVcbiAgICB9LFxuICAgIHplcFNldHRpbmdzOiB7XG4gICAgICBlbmFibGVkOiB0cnVlLFxuICAgICAgYXBpVXJsOiBwcm9jZXNzLmVudi5aRVBfQVBJX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDo4MDAxJyxcbiAgICAgIGFwaUtleTogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfWkVQX0FQSV9LRVkgfHwgcHJvY2Vzcy5lbnYuWkVQX0FQSV9LRVkgfHwgJ3lvdXItbG9jYWwtemVwLXNlY3JldC1rZXknLCAvLyBBbHdheXMgZnJvbSBlbnZcbiAgICAgIGF1dG9NZW1vcnlFeHRyYWN0aW9uOiB0cnVlLFxuICAgICAgbWVtb3J5UmV0ZW50aW9uRGF5czogMzAsXG4gICAgICBzaG93TWVtb3J5SW5zaWdodHM6IHRydWUsXG4gICAgICBtaW5GYWN0UmF0aW5nOiA3XG4gICAgfVxuICB9O1xufVxuXG4vLyBTZXR0aW5ncyBhcmUgbm93IHN0b3JlZCBpbiB0aGUgZGF0YWJhc2VcblxuLy8gSGVscGVyIGZ1bmN0aW9uIHRvIG1lcmdlIHNldHRpbmdzIHdpdGggZGVmYXVsdHMsIGVuc3VyaW5nIGVudmlyb25tZW50IHZhcmlhYmxlcyB0YWtlIHByZWNlZGVuY2VcbmZ1bmN0aW9uIG1lcmdlV2l0aERlZmF1bHRzKHNldHRpbmdzOiBQYXJ0aWFsPFVzZXJTZXR0aW5ncz4pOiBVc2VyU2V0dGluZ3Mge1xuICBjb25zdCBkZWZhdWx0cyA9IGdldERlZmF1bHRTZXR0aW5ncygpO1xuXG4gIGNvbnN0IG1lcmdlZCA9IHtcbiAgICAuLi5kZWZhdWx0cyxcbiAgICAuLi5zZXR0aW5ncywgLy8gVXNlciBzZXR0aW5ncyBvdmVycmlkZSBkZWZhdWx0c1xuICAgIHByZWZlcnJlZE1vZGVsczoge1xuICAgICAgLi4uZGVmYXVsdHMucHJlZmVycmVkTW9kZWxzLFxuICAgICAgLi4uc2V0dGluZ3MucHJlZmVycmVkTW9kZWxzLCAvLyBVc2VyIG1vZGVsIHByZWZlcmVuY2VzIG92ZXJyaWRlIGRlZmF1bHRzXG4gICAgfSxcbiAgICBwcm92aWRlclNldHRpbmdzOiB7XG4gICAgICAuLi5kZWZhdWx0cy5wcm92aWRlclNldHRpbmdzLFxuICAgICAgLi4uc2V0dGluZ3MucHJvdmlkZXJTZXR0aW5ncywgLy8gVXNlciBwcm92aWRlciBzZXR0aW5ncyBvdmVycmlkZSBkZWZhdWx0c1xuICAgICAgLy8gQWx3YXlzIG92ZXJyaWRlIEFQSSBrZXkgd2l0aCBlbnZpcm9ubWVudCB2YXJpYWJsZSAobmV2ZXIgZnJvbSB1c2VyIHNldHRpbmdzKVxuICAgICAgYXBpS2V5OiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19DSFVURVNfQVBJX1RPS0VOIHx8ICcnLFxuICAgIH0sXG4gICAgYWdlbnRTZXR0aW5nczoge1xuICAgICAgLi4uZGVmYXVsdHMuYWdlbnRTZXR0aW5ncyxcbiAgICAgIC4uLnNldHRpbmdzLmFnZW50U2V0dGluZ3MsIC8vIFVzZXIgYWdlbnQgc2V0dGluZ3Mgb3ZlcnJpZGUgZGVmYXVsdHNcbiAgICB9LFxuICAgIG9yY2hlc3RyYXRvclNldHRpbmdzOiB7XG4gICAgICAuLi5kZWZhdWx0cy5vcmNoZXN0cmF0b3JTZXR0aW5ncyxcbiAgICAgIC4uLnNldHRpbmdzLm9yY2hlc3RyYXRvclNldHRpbmdzLCAvLyBVc2VyIG9yY2hlc3RyYXRvciBzZXR0aW5ncyBvdmVycmlkZSBkZWZhdWx0c1xuICAgIH0sXG4gICAgemVwU2V0dGluZ3M6IHtcbiAgICAgIC4uLmRlZmF1bHRzLnplcFNldHRpbmdzLFxuICAgICAgLi4uc2V0dGluZ3MuemVwU2V0dGluZ3MsIC8vIFVzZXIgemVwIHNldHRpbmdzIG92ZXJyaWRlIGRlZmF1bHRzXG4gICAgICAvLyBBbHdheXMgb3ZlcnJpZGUgQVBJIGtleSB3aXRoIGVudmlyb25tZW50IHZhcmlhYmxlIChuZXZlciBmcm9tIHVzZXIgc2V0dGluZ3MpXG4gICAgICBhcGlLZXk6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1pFUF9BUElfS0VZIHx8IHByb2Nlc3MuZW52LlpFUF9BUElfS0VZIHx8ICd5b3VyLWxvY2FsLXplcC1zZWNyZXQta2V5JyxcbiAgICB9LFxuICB9O1xuXG4gIHJldHVybiBtZXJnZWQ7XG59XG5cbi8vIEdFVCAtIFJldHJpZXZlIHVzZXIgc2V0dGluZ3NcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IHNlYXJjaFBhcmFtcyB9ID0gbmV3IFVSTChyZXF1ZXN0LnVybCk7XG4gICAgY29uc3QgdXNlcklkID0gc2VhcmNoUGFyYW1zLmdldCgndXNlcklkJyk7XG4gICAgXG4gICAgaWYgKCF1c2VySWQpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnVXNlciBJRCByZXF1aXJlZCcgfSwgeyBzdGF0dXM6IDQwMCB9KTtcbiAgICB9XG5cbiAgICBjb25zdCBzZXR0aW5ncyA9IGF3YWl0IERhdGFiYXNlU2VydmljZS5sb2FkVXNlclNldHRpbmdzKHVzZXJJZCwgJ3VzZXJTZXR0aW5ncycpO1xuICAgIGNvbnN0IG1lcmdlZFNldHRpbmdzID0gbWVyZ2VXaXRoRGVmYXVsdHMoc2V0dGluZ3MgfHwge30pO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihtZXJnZWRTZXR0aW5ncyk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgc2V0dGluZ3M6JywgZXJyb3IpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdGYWlsZWQgdG8gZmV0Y2ggc2V0dGluZ3MnIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApO1xuICB9XG59XG5cbi8vIFBPU1QgLSBVcGRhdGUgdXNlciBzZXR0aW5nc1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IHNlYXJjaFBhcmFtcyB9ID0gbmV3IFVSTChyZXF1ZXN0LnVybCk7XG4gICAgY29uc3QgdXNlcklkID0gc2VhcmNoUGFyYW1zLmdldCgndXNlcklkJyk7XG4gICAgXG4gICAgaWYgKCF1c2VySWQpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnVXNlciBJRCByZXF1aXJlZCcgfSwgeyBzdGF0dXM6IDQwMCB9KTtcbiAgICB9XG5cbiAgICBjb25zdCB1cGRhdGVzOiBQYXJ0aWFsPFVzZXJTZXR0aW5ncz4gPSBhd2FpdCByZXF1ZXN0Lmpzb24oKTtcbiAgICBcbiAgICAvLyBWYWxpZGF0ZSB0aGUgdXBkYXRlcyAoYmFzaWMgdmFsaWRhdGlvbilcbiAgICBpZiAodHlwZW9mIHVwZGF0ZXMgIT09ICdvYmplY3QnIHx8IHVwZGF0ZXMgPT09IG51bGwpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogJ0ludmFsaWQgc2V0dGluZ3MgZm9ybWF0JyB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICk7XG4gICAgfVxuICAgIFxuICAgIHRyeSB7XG4gICAgICAvLyBUcnkgZGF0YWJhc2UgZmlyc3RcbiAgICAgIGlmIChhd2FpdCBEYXRhYmFzZVNlcnZpY2UuaGVhbHRoQ2hlY2soKSkge1xuICAgICAgICBjb25zdCBleGlzdGluZ1NldHRpbmdzID0gYXdhaXQgRGF0YWJhc2VTZXJ2aWNlLmxvYWRVc2VyU2V0dGluZ3ModXNlcklkLCAndXNlclNldHRpbmdzJykgfHwge307XG4gICAgICAgIGNvbnN0IGRlZmF1bHRzID0gZ2V0RGVmYXVsdFNldHRpbmdzKCk7XG5cbiAgICAgICAgLy8gTWVyZ2UgdXBkYXRlcyB3aXRoIGV4aXN0aW5nIHNldHRpbmdzLCB1c2luZyBkZWZhdWx0cyBhcyBiYXNlXG4gICAgICAgIGNvbnN0IG1lcmdlZFNldHRpbmdzID0ge1xuICAgICAgICAgIC4uLmRlZmF1bHRzLFxuICAgICAgICAgIC4uLmV4aXN0aW5nU2V0dGluZ3MsXG4gICAgICAgICAgLi4udXBkYXRlcyxcbiAgICAgICAgICBwcmVmZXJyZWRNb2RlbHM6IHtcbiAgICAgICAgICAgIC4uLmRlZmF1bHRzLnByZWZlcnJlZE1vZGVscyxcbiAgICAgICAgICAgIC4uLmV4aXN0aW5nU2V0dGluZ3MucHJlZmVycmVkTW9kZWxzLFxuICAgICAgICAgICAgLi4udXBkYXRlcy5wcmVmZXJyZWRNb2RlbHMsXG4gICAgICAgICAgfSxcbiAgICAgICAgICBwcm92aWRlclNldHRpbmdzOiB7XG4gICAgICAgICAgICAuLi5kZWZhdWx0cy5wcm92aWRlclNldHRpbmdzLFxuICAgICAgICAgICAgLi4uZXhpc3RpbmdTZXR0aW5ncy5wcm92aWRlclNldHRpbmdzLFxuICAgICAgICAgICAgLi4udXBkYXRlcy5wcm92aWRlclNldHRpbmdzLFxuICAgICAgICAgICAgLy8gQWx3YXlzIG92ZXJyaWRlIEFQSSBrZXkgd2l0aCBlbnZpcm9ubWVudCB2YXJpYWJsZVxuICAgICAgICAgICAgYXBpS2V5OiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19DSFVURVNfQVBJX1RPS0VOIHx8ICcnLFxuICAgICAgICAgIH0sXG4gICAgICAgICAgYWdlbnRTZXR0aW5nczoge1xuICAgICAgICAgICAgLi4uZGVmYXVsdHMuYWdlbnRTZXR0aW5ncyxcbiAgICAgICAgICAgIC4uLmV4aXN0aW5nU2V0dGluZ3MuYWdlbnRTZXR0aW5ncyxcbiAgICAgICAgICAgIC4uLnVwZGF0ZXMuYWdlbnRTZXR0aW5ncyxcbiAgICAgICAgICB9LFxuICAgICAgICAgIG9yY2hlc3RyYXRvclNldHRpbmdzOiB7XG4gICAgICAgICAgICAuLi5kZWZhdWx0cy5vcmNoZXN0cmF0b3JTZXR0aW5ncyxcbiAgICAgICAgICAgIC4uLmV4aXN0aW5nU2V0dGluZ3Mub3JjaGVzdHJhdG9yU2V0dGluZ3MsXG4gICAgICAgICAgICAuLi51cGRhdGVzLm9yY2hlc3RyYXRvclNldHRpbmdzLFxuICAgICAgICAgIH0sXG4gICAgICAgICAgemVwU2V0dGluZ3M6IHtcbiAgICAgICAgICAgIC4uLmRlZmF1bHRzLnplcFNldHRpbmdzLFxuICAgICAgICAgICAgLi4uZXhpc3RpbmdTZXR0aW5ncy56ZXBTZXR0aW5ncyxcbiAgICAgICAgICAgIC4uLnVwZGF0ZXMuemVwU2V0dGluZ3MsXG4gICAgICAgICAgICAvLyBBbHdheXMgb3ZlcnJpZGUgQVBJIGtleSB3aXRoIGVudmlyb25tZW50IHZhcmlhYmxlXG4gICAgICAgICAgICBhcGlLZXk6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1pFUF9BUElfS0VZIHx8IHByb2Nlc3MuZW52LlpFUF9BUElfS0VZIHx8ICd5b3VyLWxvY2FsLXplcC1zZWNyZXQta2V5JyxcbiAgICAgICAgICB9LFxuICAgICAgICB9O1xuICAgICAgICBcbiAgICAgICAgYXdhaXQgRGF0YWJhc2VTZXJ2aWNlLnNhdmVVc2VyU2V0dGluZ3ModXNlcklkLCAndXNlclNldHRpbmdzJywgbWVyZ2VkU2V0dGluZ3MpO1xuICAgICAgICBcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgc3VjY2VzczogdHJ1ZSB9KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgICB7IGVycm9yOiAnRGF0YWJhc2Ugc2VydmljZSB1bmF2YWlsYWJsZScgfSxcbiAgICAgICAgICB7IHN0YXR1czogNTAzIH1cbiAgICAgICAgKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChkYkVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdEYXRhYmFzZSBlcnJvcjonLCBkYkVycm9yKTtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogJ0RhdGFiYXNlIG9wZXJhdGlvbiBmYWlsZWQnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICAgKTtcbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgc2V0dGluZ3M6JywgZXJyb3IpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdGYWlsZWQgdG8gdXBkYXRlIHNldHRpbmdzJyB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKTtcbiAgfVxufVxuXG4vLyBERUxFVEUgLSBSZXNldCBzZXR0aW5ncyB0byBkZWZhdWx0c1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIERFTEVURShyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IHsgc2VhcmNoUGFyYW1zIH0gPSBuZXcgVVJMKHJlcXVlc3QudXJsKTtcbiAgICBjb25zdCB1c2VySWQgPSBzZWFyY2hQYXJhbXMuZ2V0KCd1c2VySWQnKTtcbiAgICBcbiAgICBpZiAoIXVzZXJJZCkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdVc2VyIElEIHJlcXVpcmVkJyB9LCB7IHN0YXR1czogNDAwIH0pO1xuICAgIH1cblxuICAgIC8vIENsZWFyIHVzZXIgc2V0dGluZ3MgZnJvbSBkYXRhYmFzZSAocmVzZXQgdG8gZGVmYXVsdHMpXG4gICAgYXdhaXQgRGF0YWJhc2VTZXJ2aWNlLnNhdmVVc2VyU2V0dGluZ3ModXNlcklkLCAndXNlclNldHRpbmdzJywge30pO1xuICAgIFxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IHN1Y2Nlc3M6IHRydWUgfSk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgcmVzZXR0aW5nIHNldHRpbmdzOicsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IGVycm9yOiAnRmFpbGVkIHRvIHJlc2V0IHNldHRpbmdzJyB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKTtcbiAgfVxufSJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJEYXRhYmFzZVNlcnZpY2UiLCJTRVRUSU5HU19TVE9SQUdFX0tFWSIsImdldERlZmF1bHRTZXR0aW5ncyIsInRoZW1lIiwiZGVmYXVsdE1vZGUiLCJhdXRvU2F2ZSIsInByZWZlcnJlZE1vZGVscyIsInNpbXBsZSIsImVuaGFuY2VkIiwib3JjaGVzdHJhdG9yIiwicmVmaW5lIiwibWVtb3J5U3VtbWFyaXphdGlvbiIsInByb3ZpZGVyU2V0dGluZ3MiLCJiYXNlVXJsIiwicHJvY2VzcyIsImVudiIsIkNIVVRFU19BUElfVVJMIiwiYXBpS2V5IiwiTkVYVF9QVUJMSUNfQ0hVVEVTX0FQSV9UT0tFTiIsImF2YWlsYWJsZU1vZGVscyIsImFnZW50U2V0dGluZ3MiLCJtYXhJdGVyYXRpb25zIiwidGVtcGVyYXR1cmUiLCJlbmFibGVUb29scyIsIm9yY2hlc3RyYXRvclNldHRpbmdzIiwicGFyYWxsZWxBZ2VudHMiLCJ0YXNrVGltZW91dCIsImFnZ3JlZ2F0aW9uU3RyYXRlZ3kiLCJlbmFibGVDb2xsYWJvcmF0aW9uIiwiemVwU2V0dGluZ3MiLCJlbmFibGVkIiwiYXBpVXJsIiwiWkVQX0FQSV9VUkwiLCJORVhUX1BVQkxJQ19aRVBfQVBJX0tFWSIsIlpFUF9BUElfS0VZIiwiYXV0b01lbW9yeUV4dHJhY3Rpb24iLCJtZW1vcnlSZXRlbnRpb25EYXlzIiwic2hvd01lbW9yeUluc2lnaHRzIiwibWluRmFjdFJhdGluZyIsIm1lcmdlV2l0aERlZmF1bHRzIiwic2V0dGluZ3MiLCJkZWZhdWx0cyIsIm1lcmdlZCIsIkdFVCIsInJlcXVlc3QiLCJzZWFyY2hQYXJhbXMiLCJVUkwiLCJ1cmwiLCJ1c2VySWQiLCJnZXQiLCJqc29uIiwiZXJyb3IiLCJzdGF0dXMiLCJsb2FkVXNlclNldHRpbmdzIiwibWVyZ2VkU2V0dGluZ3MiLCJjb25zb2xlIiwiUE9TVCIsInVwZGF0ZXMiLCJoZWFsdGhDaGVjayIsImV4aXN0aW5nU2V0dGluZ3MiLCJzYXZlVXNlclNldHRpbmdzIiwic3VjY2VzcyIsImRiRXJyb3IiLCJERUxFVEUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/settings/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DatabaseService: () => (/* binding */ DatabaseService)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// Database configuration\nconst dbConfig = {\n    host: process.env.DB_HOST || 'localhost',\n    port: parseInt(process.env.DB_PORT || '5432'),\n    database: process.env.DB_NAME || 'ai_chat_app',\n    user: process.env.DB_USER || 'postgres',\n    password: process.env.DB_PASSWORD || 'nexus123',\n    max: 20,\n    idleTimeoutMillis: 30000,\n    connectionTimeoutMillis: 10000,\n    // Disable SSL for local development\n    ssl: false\n};\n// Debug logging\nconsole.log('Database config:', {\n    host: dbConfig.host,\n    port: dbConfig.port,\n    database: dbConfig.database,\n    user: dbConfig.user,\n    password: dbConfig.password === '' ? 'empty_string' : dbConfig.password ? '***' : 'undefined'\n});\n// Create connection pool\nlet pool = null;\nfunction getPool() {\n    if (!pool) {\n        pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool(dbConfig);\n        // Handle pool errors\n        pool.on('error', (err)=>{\n            console.error('Unexpected error on idle client', err);\n        });\n    }\n    return pool;\n}\n// Database utility functions\nclass DatabaseService {\n    static async getClient() {\n        const pool = getPool();\n        return await pool.connect();\n    }\n    // Initialize database (create tables if they don't exist)\n    static async initialize() {\n        const client = await this.getClient();\n        try {\n            // Read and execute schema\n            const fs = (__webpack_require__(/*! fs */ \"fs\").promises);\n            const path = __webpack_require__(/*! path */ \"path\");\n            const schemaPath = path.join(process.cwd(), 'database', 'schema.sql');\n            try {\n                const schema = await fs.readFile(schemaPath, 'utf-8');\n                await client.query(schema);\n                console.log('Database schema initialized successfully');\n            } catch (error) {\n                console.warn('Could not read schema file, database may need manual setup:', error);\n            }\n        } finally{\n            client.release();\n        }\n    }\n    // User management\n    static async findOrCreateUser(deviceId) {\n        const client = await this.getClient();\n        try {\n            // Try to find existing user\n            const findResult = await client.query('SELECT id FROM users WHERE device_id = $1', [\n                deviceId\n            ]);\n            if (findResult.rows.length > 0) {\n                return findResult.rows[0].id;\n            }\n            // Create new user\n            const createResult = await client.query('INSERT INTO users (device_id) VALUES ($1) RETURNING id', [\n                deviceId\n            ]);\n            return createResult.rows[0].id;\n        } finally{\n            client.release();\n        }\n    }\n    // Conversation management\n    static async saveConversations(deviceId, conversations) {\n        const client = await this.getClient();\n        try {\n            await client.query('BEGIN');\n            const userId = await this.findOrCreateUser(deviceId);\n            for (const conversation of conversations){\n                // Upsert conversation\n                await client.query(`\n          INSERT INTO conversations (id, user_id, title, created_at, updated_at)\n          VALUES ($1, $2, $3, $4, $5)\n          ON CONFLICT (id) DO UPDATE SET\n            title = EXCLUDED.title,\n            updated_at = EXCLUDED.updated_at\n        `, [\n                    conversation.id,\n                    userId,\n                    conversation.title,\n                    new Date(conversation.createdAt),\n                    new Date(conversation.updatedAt)\n                ]);\n                // Delete existing messages for this conversation\n                await client.query('DELETE FROM messages WHERE conversation_id = $1', [\n                    conversation.id\n                ]);\n                // Insert messages\n                for (const message of conversation.messages){\n                    await client.query(`\n            INSERT INTO messages (id, conversation_id, role, content, timestamp, is_streaming)\n            VALUES ($1, $2, $3, $4, $5, $6)\n          `, [\n                        message.id,\n                        conversation.id,\n                        message.role,\n                        message.content,\n                        new Date(message.timestamp),\n                        message.isStreaming || false\n                    ]);\n                }\n            }\n            await client.query('COMMIT');\n        } catch (error) {\n            await client.query('ROLLBACK');\n            throw error;\n        } finally{\n            client.release();\n        }\n    }\n    static async deleteMessage(deviceId, conversationId, messageId) {\n        const client = await this.getClient();\n        try {\n            await client.query('BEGIN');\n            const userId = await this.findOrCreateUser(deviceId);\n            // Verify the conversation belongs to this user\n            const convResult = await client.query('SELECT id FROM conversations WHERE id = $1 AND user_id = $2', [\n                conversationId,\n                userId\n            ]);\n            if (convResult.rows.length === 0) {\n                throw new Error('Conversation not found or access denied');\n            }\n            // Delete the specific message\n            const deleteResult = await client.query('DELETE FROM messages WHERE id = $1 AND conversation_id = $2', [\n                messageId,\n                conversationId\n            ]);\n            if (deleteResult.rowCount === 0) {\n                throw new Error('Message not found');\n            }\n            // Update conversation's updated_at timestamp\n            await client.query('UPDATE conversations SET updated_at = CURRENT_TIMESTAMP WHERE id = $1', [\n                conversationId\n            ]);\n            await client.query('COMMIT');\n        } catch (error) {\n            await client.query('ROLLBACK');\n            throw error;\n        } finally{\n            client.release();\n        }\n    }\n    static async loadConversations(deviceId) {\n        const client = await this.getClient();\n        try {\n            const userId = await this.findOrCreateUser(deviceId);\n            // Load conversations\n            const conversationsResult = await client.query(`\n        SELECT id, title, created_at, updated_at\n        FROM conversations\n        WHERE user_id = $1\n        ORDER BY updated_at DESC\n      `, [\n                userId\n            ]);\n            const conversations = [];\n            for (const convRow of conversationsResult.rows){\n                // Load messages for this conversation\n                const messagesResult = await client.query(`\n          SELECT id, role, content, timestamp, is_streaming\n          FROM messages\n          WHERE conversation_id = $1\n          ORDER BY timestamp ASC\n        `, [\n                    convRow.id\n                ]);\n                const messages = messagesResult.rows.map((msgRow)=>({\n                        id: msgRow.id,\n                        role: msgRow.role,\n                        content: msgRow.content,\n                        timestamp: msgRow.timestamp.getTime(),\n                        isStreaming: msgRow.is_streaming\n                    }));\n                conversations.push({\n                    id: convRow.id,\n                    title: convRow.title,\n                    messages,\n                    createdAt: convRow.created_at.getTime(),\n                    updatedAt: convRow.updated_at.getTime()\n                });\n            }\n            return conversations;\n        } finally{\n            client.release();\n        }\n    }\n    // Individual conversation operations\n    static async deleteConversation(deviceId, conversationId) {\n        const client = await this.getClient();\n        try {\n            await client.query('BEGIN');\n            const userId = await this.findOrCreateUser(deviceId);\n            // Verify the conversation belongs to this user\n            const convResult = await client.query('SELECT id FROM conversations WHERE id = $1 AND user_id = $2', [\n                conversationId,\n                userId\n            ]);\n            if (convResult.rows.length === 0) {\n                throw new Error('Conversation not found or access denied');\n            }\n            // Delete messages first (due to foreign key constraint)\n            await client.query('DELETE FROM messages WHERE conversation_id = $1', [\n                conversationId\n            ]);\n            // Delete the conversation\n            await client.query('DELETE FROM conversations WHERE id = $1', [\n                conversationId\n            ]);\n            // Clear current conversation if it's the one being deleted\n            await client.query('UPDATE user_current_conversation SET conversation_id = NULL WHERE user_id = $1 AND conversation_id = $2', [\n                userId,\n                conversationId\n            ]);\n            await client.query('COMMIT');\n        } catch (error) {\n            await client.query('ROLLBACK');\n            throw error;\n        } finally{\n            client.release();\n        }\n    }\n    static async updateConversationTitle(deviceId, conversationId, newTitle) {\n        const client = await this.getClient();\n        try {\n            const userId = await this.findOrCreateUser(deviceId);\n            // Verify the conversation belongs to this user and update it\n            const result = await client.query(`\n        UPDATE conversations \n        SET title = $1, updated_at = CURRENT_TIMESTAMP \n        WHERE id = $2 AND user_id = $3\n        RETURNING id\n      `, [\n                newTitle,\n                conversationId,\n                userId\n            ]);\n            if (result.rows.length === 0) {\n                throw new Error('Conversation not found or access denied');\n            }\n        } finally{\n            client.release();\n        }\n    }\n    static async updateSingleConversation(deviceId, conversation) {\n        console.log('DatabaseService.updateSingleConversation called:', {\n            deviceId,\n            conversationId: conversation.id,\n            messageCount: conversation.messages.length\n        });\n        const client = await this.getClient();\n        try {\n            await client.query('BEGIN');\n            const userId = await this.findOrCreateUser(deviceId);\n            console.log('Found/created user:', userId);\n            // Check if conversation exists, create if it doesn't\n            const convResult = await client.query('SELECT id FROM conversations WHERE id = $1 AND user_id = $2', [\n                conversation.id,\n                userId\n            ]);\n            if (convResult.rows.length === 0) {\n                console.log('Conversation not found, creating new conversation:', {\n                    conversationId: conversation.id,\n                    userId\n                });\n                // Create the conversation\n                await client.query(`\n          INSERT INTO conversations (id, user_id, title, created_at, updated_at)\n          VALUES ($1, $2, $3, $4, $5)\n        `, [\n                    conversation.id,\n                    userId,\n                    conversation.title,\n                    new Date(conversation.createdAt),\n                    new Date(conversation.updatedAt)\n                ]);\n                console.log('Created new conversation');\n            } else {\n                console.log('Conversation verified, updating...');\n                // Update existing conversation\n                await client.query(`\n          UPDATE conversations\n          SET title = $1, updated_at = $2\n          WHERE id = $3\n        `, [\n                    conversation.title,\n                    new Date(conversation.updatedAt),\n                    conversation.id\n                ]);\n                console.log('Updated existing conversation');\n            }\n            // Delete existing messages for this conversation\n            const deleteResult = await client.query('DELETE FROM messages WHERE conversation_id = $1', [\n                conversation.id\n            ]);\n            console.log('Deleted existing messages:', deleteResult.rowCount);\n            // Insert updated messages\n            for (const message of conversation.messages){\n                await client.query(`\n          INSERT INTO messages (id, conversation_id, role, content, timestamp, is_streaming)\n          VALUES ($1, $2, $3, $4, $5, $6)\n        `, [\n                    message.id,\n                    conversation.id,\n                    message.role,\n                    message.content,\n                    new Date(message.timestamp),\n                    message.isStreaming || false\n                ]);\n            }\n            console.log('Inserted updated messages:', conversation.messages.length);\n            await client.query('COMMIT');\n            console.log('Successfully updated conversation in database');\n        } catch (error) {\n            console.error('DatabaseService.updateSingleConversation error:', error);\n            await client.query('ROLLBACK');\n            throw error;\n        } finally{\n            client.release();\n        }\n    }\n    // Current conversation management\n    static async setCurrentConversation(deviceId, conversationId) {\n        const client = await this.getClient();\n        try {\n            const userId = await this.findOrCreateUser(deviceId);\n            await client.query(`\n        INSERT INTO user_current_conversation (user_id, conversation_id)\n        VALUES ($1, $2)\n        ON CONFLICT (user_id) DO UPDATE SET\n          conversation_id = EXCLUDED.conversation_id,\n          updated_at = CURRENT_TIMESTAMP\n      `, [\n                userId,\n                conversationId\n            ]);\n        } finally{\n            client.release();\n        }\n    }\n    static async getCurrentConversation(deviceId) {\n        const client = await this.getClient();\n        try {\n            const userId = await this.findOrCreateUser(deviceId);\n            const result = await client.query(`\n        SELECT conversation_id\n        FROM user_current_conversation\n        WHERE user_id = $1\n      `, [\n                userId\n            ]);\n            return result.rows.length > 0 ? result.rows[0].conversation_id : null;\n        } finally{\n            client.release();\n        }\n    }\n    // Settings management\n    static async saveUserSettings(deviceId, settingsKey, settingsValue) {\n        const client = await this.getClient();\n        try {\n            const userId = await this.findOrCreateUser(deviceId);\n            // For JSONB columns, PostgreSQL expects a JavaScript object, not a JSON string\n            // The pg driver will automatically handle the conversion to JSONB\n            if (typeof settingsValue !== 'object' || settingsValue === null) {\n                throw new Error(`Settings value must be an object, got ${typeof settingsValue}`);\n            }\n            await client.query(`\n        INSERT INTO user_settings (user_id, settings_key, settings_value)\n        VALUES ($1, $2, $3)\n        ON CONFLICT (user_id, settings_key) DO UPDATE SET\n          settings_value = EXCLUDED.settings_value,\n          updated_at = CURRENT_TIMESTAMP\n      `, [\n                userId,\n                settingsKey,\n                settingsValue\n            ]);\n        } finally{\n            client.release();\n        }\n    }\n    static async loadUserSettings(deviceId, settingsKey) {\n        const client = await this.getClient();\n        try {\n            const userId = await this.findOrCreateUser(deviceId);\n            const result = await client.query(`\n        SELECT settings_value\n        FROM user_settings\n        WHERE user_id = $1 AND settings_key = $2\n      `, [\n                userId,\n                settingsKey\n            ]);\n            if (result.rows.length > 0) {\n                const rawValue = result.rows[0].settings_value;\n                // Since the column is JSONB, PostgreSQL returns it as an object, not a string\n                // No need to JSON.parse() - it's already parsed by PostgreSQL\n                if (rawValue && typeof rawValue === 'object') {\n                    return rawValue;\n                } else {\n                    console.error(`Invalid settings data type for ${deviceId}:${settingsKey}:`, typeof rawValue);\n                    // Clear the corrupted data\n                    await client.query(`\n            DELETE FROM user_settings\n            WHERE user_id = $1 AND settings_key = $2\n          `, [\n                        userId,\n                        settingsKey\n                    ]);\n                    console.log(`Cleared corrupted settings data for ${deviceId}:${settingsKey}`);\n                    return null;\n                }\n            }\n            return null;\n        } finally{\n            client.release();\n        }\n    }\n    // Clear all data for a user\n    static async clearUserData(deviceId) {\n        const client = await this.getClient();\n        try {\n            await client.query('BEGIN');\n            const userResult = await client.query('SELECT id FROM users WHERE device_id = $1', [\n                deviceId\n            ]);\n            if (userResult.rows.length > 0) {\n                const userId = userResult.rows[0].id;\n                // Delete user data (cascading deletes will handle related records)\n                await client.query('DELETE FROM users WHERE id = $1', [\n                    userId\n                ]);\n            }\n            await client.query('COMMIT');\n        } catch (error) {\n            await client.query('ROLLBACK');\n            throw error;\n        } finally{\n            client.release();\n        }\n    }\n    // Health check\n    static async healthCheck() {\n        try {\n            const client = await this.getClient();\n            try {\n                await client.query('SELECT 1');\n                return true;\n            } finally{\n                client.release();\n            }\n        } catch (error) {\n            console.error('Database health check failed:', error);\n            return false;\n        }\n    }\n    // Close pool (for graceful shutdown)\n    static async close() {\n        if (pool) {\n            await pool.end();\n            pool = null;\n        }\n    }\n}\n// Initialize database on module load\nif (true) {\n    DatabaseService.initialize().catch(console.error);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsettings%2Froute&page=%2Fapi%2Fsettings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Froute.ts&appDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();