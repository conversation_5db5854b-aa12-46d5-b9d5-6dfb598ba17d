"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hastscript";
exports.ids = ["vendor-chunks/hastscript"];
exports.modules = {

/***/ "(ssr)/./node_modules/hastscript/lib/create-h.js":
/*!*************************************************!*\
  !*** ./node_modules/hastscript/lib/create-h.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createH: () => (/* binding */ createH)\n/* harmony export */ });\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/comma-separated-tokens/index.js\");\n/* harmony import */ var hast_util_parse_selector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-parse-selector */ \"(ssr)/./node_modules/hast-util-parse-selector/lib/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/normalize.js\");\n/* harmony import */ var space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! space-separated-tokens */ \"(ssr)/./node_modules/space-separated-tokens/index.js\");\n/**\n * @import {Element, Nodes, RootContent, Root} from 'hast'\n * @import {Info, Schema} from 'property-information'\n */\n\n/**\n * @typedef {Array<Nodes | PrimitiveChild>} ArrayChildNested\n *   List of children (deep).\n */\n\n/**\n * @typedef {Array<ArrayChildNested | Nodes | PrimitiveChild>} ArrayChild\n *   List of children.\n */\n\n/**\n * @typedef {Array<number | string>} ArrayValue\n *   List of property values for space- or comma separated values (such as `className`).\n */\n\n/**\n * @typedef {ArrayChild | Nodes | PrimitiveChild} Child\n *   Acceptable child value.\n */\n\n/**\n * @typedef {number | string | null | undefined} PrimitiveChild\n *   Primitive children, either ignored (nullish), or turned into text nodes.\n */\n\n/**\n * @typedef {boolean | number | string | null | undefined} PrimitiveValue\n *   Primitive property value.\n */\n\n/**\n * @typedef {Record<string, PropertyValue | Style>} Properties\n *   Acceptable value for element properties.\n */\n\n/**\n * @typedef {ArrayValue | PrimitiveValue} PropertyValue\n *   Primitive value or list value.\n */\n\n/**\n * @typedef {Element | Root} Result\n *   Result from a `h` (or `s`) call.\n */\n\n/**\n * @typedef {number | string} StyleValue\n *   Value for a CSS style field.\n */\n\n/**\n * @typedef {Record<string, StyleValue>} Style\n *   Supported value of a `style` prop.\n */\n\n\n\n\n\n\n/**\n * @param {Schema} schema\n *   Schema to use.\n * @param {string} defaultTagName\n *   Default tag name.\n * @param {ReadonlyArray<string> | undefined} [caseSensitive]\n *   Case-sensitive tag names (default: `undefined`).\n * @returns\n *   `h`.\n */\nfunction createH(schema, defaultTagName, caseSensitive) {\n  const adjust = caseSensitive ? createAdjustMap(caseSensitive) : undefined\n\n  /**\n   * Hyperscript compatible DSL for creating virtual hast trees.\n   *\n   * @overload\n   * @param {null | undefined} [selector]\n   * @param {...Child} children\n   * @returns {Root}\n   *\n   * @overload\n   * @param {string} selector\n   * @param {Properties} properties\n   * @param {...Child} children\n   * @returns {Element}\n   *\n   * @overload\n   * @param {string} selector\n   * @param {...Child} children\n   * @returns {Element}\n   *\n   * @param {string | null | undefined} [selector]\n   *   Selector.\n   * @param {Child | Properties | null | undefined} [properties]\n   *   Properties (or first child) (default: `undefined`).\n   * @param {...Child} children\n   *   Children.\n   * @returns {Result}\n   *   Result.\n   */\n  function h(selector, properties, ...children) {\n    /** @type {Result} */\n    let node\n\n    if (selector === null || selector === undefined) {\n      node = {type: 'root', children: []}\n      // Properties are not supported for roots.\n      const child = /** @type {Child} */ (properties)\n      children.unshift(child)\n    } else {\n      node = (0,hast_util_parse_selector__WEBPACK_IMPORTED_MODULE_0__.parseSelector)(selector, defaultTagName)\n      // Normalize the name.\n      const lower = node.tagName.toLowerCase()\n      const adjusted = adjust ? adjust.get(lower) : undefined\n      node.tagName = adjusted || lower\n\n      // Handle properties.\n      if (isChild(properties)) {\n        children.unshift(properties)\n      } else {\n        for (const [key, value] of Object.entries(properties)) {\n          addProperty(schema, node.properties, key, value)\n        }\n      }\n    }\n\n    // Handle children.\n    for (const child of children) {\n      addChild(node.children, child)\n    }\n\n    if (node.type === 'element' && node.tagName === 'template') {\n      node.content = {type: 'root', children: node.children}\n      node.children = []\n    }\n\n    return node\n  }\n\n  return h\n}\n\n/**\n * Check if something is properties or a child.\n *\n * @param {Child | Properties} value\n *   Value to check.\n * @returns {value is Child}\n *   Whether `value` is definitely a child.\n */\nfunction isChild(value) {\n  // Never properties if not an object.\n  if (value === null || typeof value !== 'object' || Array.isArray(value)) {\n    return true\n  }\n\n  // Never node without `type`; that’s the main discriminator.\n  if (typeof value.type !== 'string') return false\n\n  // Slower check: never property value if object or array with\n  // non-number/strings.\n  const record = /** @type {Record<string, unknown>} */ (value)\n  const keys = Object.keys(value)\n\n  for (const key of keys) {\n    const value = record[key]\n\n    if (value && typeof value === 'object') {\n      if (!Array.isArray(value)) return true\n\n      const list = /** @type {ReadonlyArray<unknown>} */ (value)\n\n      for (const item of list) {\n        if (typeof item !== 'number' && typeof item !== 'string') {\n          return true\n        }\n      }\n    }\n  }\n\n  // Also see empty `children` as a node.\n  if ('children' in value && Array.isArray(value.children)) {\n    return true\n  }\n\n  // Default to properties, someone can always pass an empty object,\n  // put `data: {}` in a node,\n  // or wrap it in an array.\n  return false\n}\n\n/**\n * @param {Schema} schema\n *   Schema.\n * @param {Properties} properties\n *   Properties object.\n * @param {string} key\n *   Property name.\n * @param {PropertyValue | Style} value\n *   Property value.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addProperty(schema, properties, key, value) {\n  const info = (0,property_information__WEBPACK_IMPORTED_MODULE_1__.find)(schema, key)\n  /** @type {PropertyValue} */\n  let result\n\n  // Ignore nullish and NaN values.\n  if (value === null || value === undefined) return\n\n  if (typeof value === 'number') {\n    // Ignore NaN.\n    if (Number.isNaN(value)) return\n\n    result = value\n  }\n  // Booleans.\n  else if (typeof value === 'boolean') {\n    result = value\n  }\n  // Handle list values.\n  else if (typeof value === 'string') {\n    if (info.spaceSeparated) {\n      result = (0,space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__.parse)(value)\n    } else if (info.commaSeparated) {\n      result = (0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__.parse)(value)\n    } else if (info.commaOrSpaceSeparated) {\n      result = (0,space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__.parse)((0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__.parse)(value).join(' '))\n    } else {\n      result = parsePrimitive(info, info.property, value)\n    }\n  } else if (Array.isArray(value)) {\n    result = [...value]\n  } else {\n    result = info.property === 'style' ? style(value) : String(value)\n  }\n\n  if (Array.isArray(result)) {\n    /** @type {Array<number | string>} */\n    const finalResult = []\n\n    for (const item of result) {\n      // Assume no booleans in array.\n      finalResult.push(\n        /** @type {number | string} */ (\n          parsePrimitive(info, info.property, item)\n        )\n      )\n    }\n\n    result = finalResult\n  }\n\n  // Class names (which can be added both on the `selector` and here).\n  if (info.property === 'className' && Array.isArray(properties.className)) {\n    // Assume no booleans in `className`.\n    result = properties.className.concat(\n      /** @type {Array<number | string> | number | string} */ (result)\n    )\n  }\n\n  properties[info.property] = result\n}\n\n/**\n * @param {Array<RootContent>} nodes\n *   Children.\n * @param {Child} value\n *   Child.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addChild(nodes, value) {\n  if (value === null || value === undefined) {\n    // Empty.\n  } else if (typeof value === 'number' || typeof value === 'string') {\n    nodes.push({type: 'text', value: String(value)})\n  } else if (Array.isArray(value)) {\n    for (const child of value) {\n      addChild(nodes, child)\n    }\n  } else if (typeof value === 'object' && 'type' in value) {\n    if (value.type === 'root') {\n      addChild(nodes, value.children)\n    } else {\n      nodes.push(value)\n    }\n  } else {\n    throw new Error('Expected node, nodes, or string, got `' + value + '`')\n  }\n}\n\n/**\n * Parse a single primitives.\n *\n * @param {Info} info\n *   Property information.\n * @param {string} name\n *   Property name.\n * @param {PrimitiveValue} value\n *   Property value.\n * @returns {PrimitiveValue}\n *   Property value.\n */\nfunction parsePrimitive(info, name, value) {\n  if (typeof value === 'string') {\n    if (info.number && value && !Number.isNaN(Number(value))) {\n      return Number(value)\n    }\n\n    if (\n      (info.boolean || info.overloadedBoolean) &&\n      (value === '' || (0,property_information__WEBPACK_IMPORTED_MODULE_4__.normalize)(value) === (0,property_information__WEBPACK_IMPORTED_MODULE_4__.normalize)(name))\n    ) {\n      return true\n    }\n  }\n\n  return value\n}\n\n/**\n * Serialize a `style` object as a string.\n *\n * @param {Style} styles\n *   Style object.\n * @returns {string}\n *   CSS string.\n */\nfunction style(styles) {\n  /** @type {Array<string>} */\n  const result = []\n\n  for (const [key, value] of Object.entries(styles)) {\n    result.push([key, value].join(': '))\n  }\n\n  return result.join('; ')\n}\n\n/**\n * Create a map to adjust casing.\n *\n * @param {ReadonlyArray<string>} values\n *   List of properly cased keys.\n * @returns {Map<string, string>}\n *   Map of lowercase keys to uppercase keys.\n */\nfunction createAdjustMap(values) {\n  /** @type {Map<string, string>} */\n  const result = new Map()\n\n  for (const value of values) {\n    result.set(value.toLowerCase(), value)\n  }\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/lib/create-h.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/lib/index.js":
/*!**********************************************!*\
  !*** ./node_modules/hastscript/lib/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   h: () => (/* binding */ h),\n/* harmony export */   s: () => (/* binding */ s)\n/* harmony export */ });\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var _create_h_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./create-h.js */ \"(ssr)/./node_modules/hastscript/lib/create-h.js\");\n/* harmony import */ var _svg_case_sensitive_tag_names_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./svg-case-sensitive-tag-names.js */ \"(ssr)/./node_modules/hastscript/lib/svg-case-sensitive-tag-names.js\");\n// Register the JSX namespace on `h`.\n/**\n * @typedef {import('./jsx-classic.js').Element} h.JSX.Element\n * @typedef {import('./jsx-classic.js').ElementChildrenAttribute} h.JSX.ElementChildrenAttribute\n * @typedef {import('./jsx-classic.js').IntrinsicAttributes} h.JSX.IntrinsicAttributes\n * @typedef {import('./jsx-classic.js').IntrinsicElements} h.JSX.IntrinsicElements\n */\n\n// Register the JSX namespace on `s`.\n/**\n * @typedef {import('./jsx-classic.js').Element} s.JSX.Element\n * @typedef {import('./jsx-classic.js').ElementChildrenAttribute} s.JSX.ElementChildrenAttribute\n * @typedef {import('./jsx-classic.js').IntrinsicAttributes} s.JSX.IntrinsicAttributes\n * @typedef {import('./jsx-classic.js').IntrinsicElements} s.JSX.IntrinsicElements\n */\n\n\n\n\n\n// Note: this explicit type is needed, otherwise TS creates broken types.\n/** @type {ReturnType<createH>} */\nconst h = (0,_create_h_js__WEBPACK_IMPORTED_MODULE_0__.createH)(property_information__WEBPACK_IMPORTED_MODULE_1__.html, 'div')\n\n// Note: this explicit type is needed, otherwise TS creates broken types.\n/** @type {ReturnType<createH>} */\nconst s = (0,_create_h_js__WEBPACK_IMPORTED_MODULE_0__.createH)(property_information__WEBPACK_IMPORTED_MODULE_1__.svg, 'g', _svg_case_sensitive_tag_names_js__WEBPACK_IMPORTED_MODULE_2__.svgCaseSensitiveTagNames)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/lib/svg-case-sensitive-tag-names.js":
/*!*********************************************************************!*\
  !*** ./node_modules/hastscript/lib/svg-case-sensitive-tag-names.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   svgCaseSensitiveTagNames: () => (/* binding */ svgCaseSensitiveTagNames)\n/* harmony export */ });\n/**\n * List of case-sensitive SVG tag names.\n *\n * @type {ReadonlyArray<string>}\n */\nconst svgCaseSensitiveTagNames = [\n  'altGlyph',\n  'altGlyphDef',\n  'altGlyphItem',\n  'animateColor',\n  'animateMotion',\n  'animateTransform',\n  'clipPath',\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feDropShadow',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n  'foreignObject',\n  'glyphRef',\n  'linearGradient',\n  'radialGradient',\n  'solidColor',\n  'textArea',\n  'textPath'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9saWIvc3ZnLWNhc2Utc2Vuc2l0aXZlLXRhZy1uYW1lcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSXFxEZXZcXEFJXFxDaGF0XFxjaGF0MTFcXGFpLWNoYXQtYXBwXFxub2RlX21vZHVsZXNcXGhhc3RzY3JpcHRcXGxpYlxcc3ZnLWNhc2Utc2Vuc2l0aXZlLXRhZy1uYW1lcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIExpc3Qgb2YgY2FzZS1zZW5zaXRpdmUgU1ZHIHRhZyBuYW1lcy5cbiAqXG4gKiBAdHlwZSB7UmVhZG9ubHlBcnJheTxzdHJpbmc+fVxuICovXG5leHBvcnQgY29uc3Qgc3ZnQ2FzZVNlbnNpdGl2ZVRhZ05hbWVzID0gW1xuICAnYWx0R2x5cGgnLFxuICAnYWx0R2x5cGhEZWYnLFxuICAnYWx0R2x5cGhJdGVtJyxcbiAgJ2FuaW1hdGVDb2xvcicsXG4gICdhbmltYXRlTW90aW9uJyxcbiAgJ2FuaW1hdGVUcmFuc2Zvcm0nLFxuICAnY2xpcFBhdGgnLFxuICAnZmVCbGVuZCcsXG4gICdmZUNvbG9yTWF0cml4JyxcbiAgJ2ZlQ29tcG9uZW50VHJhbnNmZXInLFxuICAnZmVDb21wb3NpdGUnLFxuICAnZmVDb252b2x2ZU1hdHJpeCcsXG4gICdmZURpZmZ1c2VMaWdodGluZycsXG4gICdmZURpc3BsYWNlbWVudE1hcCcsXG4gICdmZURpc3RhbnRMaWdodCcsXG4gICdmZURyb3BTaGFkb3cnLFxuICAnZmVGbG9vZCcsXG4gICdmZUZ1bmNBJyxcbiAgJ2ZlRnVuY0InLFxuICAnZmVGdW5jRycsXG4gICdmZUZ1bmNSJyxcbiAgJ2ZlR2F1c3NpYW5CbHVyJyxcbiAgJ2ZlSW1hZ2UnLFxuICAnZmVNZXJnZScsXG4gICdmZU1lcmdlTm9kZScsXG4gICdmZU1vcnBob2xvZ3knLFxuICAnZmVPZmZzZXQnLFxuICAnZmVQb2ludExpZ2h0JyxcbiAgJ2ZlU3BlY3VsYXJMaWdodGluZycsXG4gICdmZVNwb3RMaWdodCcsXG4gICdmZVRpbGUnLFxuICAnZmVUdXJidWxlbmNlJyxcbiAgJ2ZvcmVpZ25PYmplY3QnLFxuICAnZ2x5cGhSZWYnLFxuICAnbGluZWFyR3JhZGllbnQnLFxuICAncmFkaWFsR3JhZGllbnQnLFxuICAnc29saWRDb2xvcicsXG4gICd0ZXh0QXJlYScsXG4gICd0ZXh0UGF0aCdcbl1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/lib/svg-case-sensitive-tag-names.js\n");

/***/ })

};
;